// ==UserScript==
// @name         Claude Session Key 管理器
// @name:zh-CN   Claude Session Key 管理器
// @name:en      Claude Session Key Manager
// @version      2.2.0
// @description  Claude Session Key 管理工具，支持拖拽、测活、导入、Pro账号识别等功能
// @description:zh-CN  Claude Session Key 管理工具，支持拖拽、测活、导入、Pro账号识别等功能
// @description:en  Claude Session Key Manager with drag-and-drop, token validation, import and Pro account detection
// <AUTHOR> (optimized version)
// @namespace    https://greasyfork.org/users/1317128-xiaoye6688
// @license      MIT
// @date         2025-03-19
// @modified     2025-03-19

// @match        https://claude.ai/*
// @match        https://claude.asia/*
// @match        https://demo.fuclaude.com/*
// @include      https://*fuclaude*/*
//
// @icon         https://claude.ai/favicon.ico
// @run-at       document-end

// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_addStyle
// @grant        GM_openInTab
// @grant        GM_cookie

// @connect      ipapi.co
// @connect      api.claude.ai
// @connect      claude.ai
// ==/UserScript==

(function() {
    "use strict";

    // =============== 配置与常量 ===============
    const CONFIG = {
        storageKey: "claudeTokens",
        ipApiUrl: "https://ipapi.co/country_code",
        defaultToken: { name: "Token01", key: "sk-ant-sid", accountType: undefined },
        currentTokenKey: "currentClaudeToken",
        testResultsKey: "claudeTokenTestResults",
        testResultExpiry: 1800000, // 30分钟过期
        physicsTimings: {
            dropReveal: 280,      // 下拉菜单显示时间
            dropHide: 220,        // 下拉菜单隐藏时间
            buttonHover: 180,     // 按钮悬停反应时间
            statusDuration: 2800, // 状态消息停留时间
            testDelay: 80,        // 测试序列延迟
            tooltipDelay: 800     // 工具提示延迟
        },
        physics: {
            spring: "cubic-bezier(.17,.67,.23,1.1)",      // 弹性曲线
            bounce: "cubic-bezier(.2,1.18,.67,1.11)",     // 弹跳曲线
            snap: "cubic-bezier(.34,1.56,.64,1)",         // 迅速曲线
            enter: "cubic-bezier(.15,1.16,.42,1.12)",     // 进入曲线
            exit: "cubic-bezier(.5,-.2,.4,1.3)",          // 退出曲线
            scale: "cubic-bezier(.54,.02,.31,1.28)",      // 缩放曲线
            swiftOut: "cubic-bezier(.18,.89,.32,1.28)",   // 快速退出
            gentleIn: "cubic-bezier(.4,0,.2,1)"           // 平滑进入
        },
        switchMode: "reload" // 切换token模式: redirect=新标签页、reload=刷新
    };

    // =============== 工具与辅助函数 ===============
    /**
     * 获取主题数据，支持暗色和亮色主题
     * @param {boolean} isDark - 是否暗色主题
     * @returns {Object} 主题变量
     */
    const getTheme = (isDark) => ({
        // 颜色
        bg: isDark ? "#232323" : "#fcfcfa",
        text: isDark ? "#f0f0f0" : "#333333",
        border: isDark ? "#3a3a3a" : "#e5e5e5",
        buttonBg: isDark ? "#3a3a3a" : "#f5f5f5",
        buttonHover: isDark ? "#4a4a47" : "#eaeaea",
        primary: "#b84a33",
        primaryDark: "#9c3e2b",
        primaryLight: isDark ? "rgba(184, 74, 51, 0.18)" : "rgba(184, 74, 51, 0.08)",
        modalOverlay: isDark ? "rgba(0, 0, 0, 0.75)" : "rgba(0, 0, 0, 0.45)",
        success: "#48bb78",
        error: "#e53e3e",
        warning: "#ed8936",
        info: "#4299e1",

        // Pro 账号颜色 - 白底黑字简约设计
        proBg: isDark ? "rgba(255, 255, 255, 0.92)" : "rgba(255, 255, 255, 0.95)",
        proText: isDark ? "rgba(40, 40, 40, 0.95)" : "rgba(30, 30, 30, 0.95)",
        proBorder: isDark ? "rgba(58, 58, 58, 0.5)" : "rgba(229, 229, 229, 0.8)", // 与普通边框相同
        proShadow: "0 2px 6px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)", // 优化阴影

        // 阴影
        shadow: isDark ? "0 3px 12px rgba(0,0,0,0.2)" : "0 3px 12px rgba(0,0,0,0.08)",
        deepShadow: isDark ? "0 8px 25px rgba(0,0,0,0.3)" : "0 8px 25px rgba(0,0,0,0.15)",
        buttonShadow: isDark ? "0 2px 5px rgba(0,0,0,0.3)" : "0 2px 5px rgba(0,0,0,0.1)",
        floatShadow: "0 8px 30px rgba(0,0,0,0.15), 0 2px 15px rgba(0,0,0,0.1)",
        proShadow: isDark ? "0 3px 8px rgba(0,0,0,0.25)" : "0 3px 10px rgba(0,0,0,0.12)", // Pro专用阴影

        // 动画
        timings: CONFIG.physicsTimings,
        physics: CONFIG.physics
    });

    /**
     * DOM 操作简化工具集
     */
    const DOM = {
        create(tag, className = '', attributes = {}) {
            const elem = document.createElement(tag);
            if (className) elem.className = className;

            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'style' && typeof value === 'object') {
                    Object.assign(elem.style, value);
                } else if (key === 'text') {
                    elem.textContent = value;
                } else if (key === 'html') {
                    elem.innerHTML = value;
                } else {
                    elem.setAttribute(key, value);
                }
            });

            return elem;
        },

        html(elem, html) {
            elem.innerHTML = html;
            return elem;
        },

        text(elem, text) {
            elem.textContent = text;
            return elem;
        },

        on(elem, event, handler, options) {
            elem.addEventListener(event, handler, options);
            return elem;
        },

        off(elem, event, handler) {
            elem.removeEventListener(event, handler);
            return elem;
        },

        css(elem, styles) {
            Object.assign(elem.style, styles);
            return elem;
        },

        addClass(elem, className) {
            elem.classList.add(className);
            return elem;
        },

        removeClass(elem, className) {
            elem.classList.remove(className);
            return elem;
        },

        toggleClass(elem, className, condition) {
            if (condition === undefined) {
                elem.classList.toggle(className);
            } else {
                condition ? elem.classList.add(className) : elem.classList.remove(className);
            }
            return elem;
        },

        append(parent, child) {
            parent.appendChild(child);
            return parent;
        },

        appendAll(parent, children) {
            const fragment = document.createDocumentFragment();
            children.forEach(child => fragment.appendChild(child));
            parent.appendChild(fragment);
            return parent;
        },

        remove(elem) {
            if (elem && elem.parentNode) {
                elem.parentNode.removeChild(elem);
            }
        },

        get(selector, context = document) {
            return context.querySelector(selector);
        },

        getAll(selector, context = document) {
            return [...context.querySelectorAll(selector)];
        },

        transition(elem, styles, duration, timingFunction, callback = null) {
            const originalTransition = elem.style.transition;

            elem.style.transition = Object.keys(styles)
                .map(prop => `${prop} ${duration}ms ${timingFunction}`)
                .join(', ');

            if (callback) {
                const onTransitionEnd = (e) => {
                    if (e.target === elem) {
                        elem.removeEventListener('transitionend', onTransitionEnd);
                        elem.style.transition = originalTransition;
                        callback();
                    }
                };
                elem.addEventListener('transitionend', onTransitionEnd);
            }

            void elem.offsetWidth; // 强制重排

            Object.assign(elem.style, styles);

            if (!callback) {
                setTimeout(() => {
                    elem.style.transition = originalTransition;
                }, duration + 50);
            }

            return elem;
        },

        async sequence(sequence, elem, isClass = false) {
            for (let step of sequence) {
                await new Promise(resolve => {
                    setTimeout(() => {
                        if (isClass) {
                            if (step.add) this.addClass(elem, step.add);
                            if (step.remove) this.removeClass(elem, step.remove);
                        } else {
                            Object.assign(elem.style, step);
                        }
                        resolve();
                    }, step.delay || 0);
                });
            }
        }
    };

    /**
     * 动画工具集
     */
    const Animate = {
        async raf(callback, duration) {
            const startTime = performance.now();

            return new Promise(resolve => {
                const animate = (currentTime) => {
                    const elapsedTime = currentTime - startTime;
                    const progress = Math.min(elapsedTime / duration, 1);

                    callback(progress);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        resolve();
                    }
                };

                requestAnimationFrame(animate);
            });
        },

        stagger(elements, animateFunc, delay = 50, staggerIndex = 0) {
            elements.forEach((el, i) => {
                setTimeout(() => {
                    animateFunc(el, i);
                }, delay * Math.abs(i - staggerIndex));
            });
        },

        async bounce(elem, intensity = 0.5) {
            const original = {
                transform: elem.style.transform,
                transition: elem.style.transition
            };

            elem.style.transition = `transform 120ms ${CONFIG.physics.bounce}`;
            elem.style.transform = `scale(${1 + 0.15 * intensity})`;

            await new Promise(r => setTimeout(r, 120));

            elem.style.transition = `transform 180ms ${CONFIG.physics.spring}`;
            elem.style.transform = `scale(${1 - 0.05 * intensity})`;

            await new Promise(r => setTimeout(r, 180));

            elem.style.transition = `transform 150ms ${CONFIG.physics.snap}`;
            elem.style.transform = original.transform || 'scale(1)';

            await new Promise(r => setTimeout(r, 150));
            elem.style.transition = original.transition;

            return elem;
        },

        async pulse(elem, property, from, to, duration = 900) {
            const easingFunc = t => 0.5 * (1 - Math.cos(Math.PI * t));
            const original = elem.style[property];

            await this.raf(progress => {
                const direction = progress < 0.5 ? progress * 2 : (1 - progress) * 2;
                const pulseProgress = easingFunc(direction);
                const value = from + (to - from) * pulseProgress;

                elem.style[property] = typeof value === 'number' ? `${value}px` : value;
            }, duration);

            elem.style[property] = original;
            return elem;
        },

        async shake(elem, intensity = 5, duration = 400) {
            const originalTransform = elem.style.transform || '';
            const steps = 7;
            const stepDuration = duration / steps;

            for (let i = 0; i < steps; i++) {
                const offset = intensity * Math.sin(Math.PI * i) * (steps - i) / steps;
                await new Promise(resolve => {
                    elem.style.transform = `${originalTransform} translateX(${offset}px)`;
                    setTimeout(resolve, stepDuration);
                });
            }

            elem.style.transform = originalTransform;
            return elem;
        }
    };

    /**
     * 通知/状态UI工具
     */
    const Notify = {
        status(message, type = 'info', duration = CONFIG.physicsTimings.statusDuration) {
            const existing = document.querySelector('.claude-status-message');
            if (existing) {
                DOM.transition(existing,
                    { opacity: 0, transform: 'translateY(20px)' },
                    200, CONFIG.physics.gentleIn,
                    () => DOM.remove(existing)
                );
            }

            const msgElem = DOM.create('div', `claude-status-message ${type}`, {
                text: message,
                style: {
                    position: 'fixed',
                    bottom: '24px',
                    right: '24px',
                    padding: '14px 20px',
                    borderRadius: '12px',
                    maxWidth: '320px',
                    backgroundColor: 'var(--bg-color)',
                    color: 'var(--text-color)',
                    boxShadow: 'var(--deep-shadow)',
                    zIndex: 10002,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    borderLeft: `4px solid var(--${type}-color)`,
                    opacity: 0,
                    transform: 'translateY(30px) scale(0.95)',
                    transition: `opacity 400ms ${CONFIG.physics.enter}, transform 400ms ${CONFIG.physics.enter}`
                }
            });

            let iconChar = '';
            switch(type) {
                case 'success': iconChar = '✓'; break;
                case 'error': iconChar = '✕'; break;
                case 'warning': iconChar = '⚠'; break;
                default: iconChar = 'ℹ';
            }

            const icon = DOM.create('span', '', {
                text: iconChar,
                style: {
                    fontSize: '18px',
                    fontWeight: 'bold',
                    color: `var(--${type}-color)`
                }
            });

            msgElem.insertBefore(icon, msgElem.firstChild);
            document.body.appendChild(msgElem);

            void msgElem.offsetWidth; // 强制重排

            msgElem.style.opacity = '1';
            msgElem.style.transform = 'translateY(0) scale(1)';

            setTimeout(() => {
                DOM.transition(msgElem,
                    { opacity: 0, transform: 'translateY(15px)' },
                    300, CONFIG.physics.gentleIn,
                    () => DOM.remove(msgElem)
                );
            }, duration);

            return msgElem;
        },

        confirm(title, message, confirmText = '确认', cancelText = '取消') {
            return new Promise(resolve => {
                const overlay = DOM.create('div', 'claude-modal', {
                    style: {
                        position: 'fixed',
                        top: 0, left: 0, right: 0, bottom: 0,
                        backgroundColor: 'var(--modal-overlay)',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 10001,
                        opacity: 0,
                        backdropFilter: 'blur(5px)',
                        transition: `opacity 300ms ${CONFIG.physics.gentleIn}`
                    }
                });

                const content = DOM.create('div', 'claude-modal-content', {
                    style: {
                        backgroundColor: 'var(--bg-color)',
                        padding: '28px',
                        borderRadius: '16px',
                        boxShadow: 'var(--float-shadow)',
                        width: '400px',
                        maxWidth: '90%',
                        position: 'relative',
                        transform: 'scale(0.85) translateY(20px)',
                        transition: `transform 350ms ${CONFIG.physics.spring}`,
                        textAlign: 'center'
                    }
                });

                const warningIcon = DOM.create('div', 'claude-confirm-icon', {
                    html: '⚠️',
                    style: {
                        fontSize: '56px',
                        marginBottom: '20px',
                        display: 'inline-block'
                    }
                });

                Animate.pulse(warningIcon, 'transform', 'scale(0.9)', 'scale(1.1)');

                const titleElem = DOM.create('div', 'claude-confirm-title', {
                    text: title,
                    style: {
                        fontSize: '22px',
                        fontWeight: '600',
                        marginBottom: '16px',
                        color: 'var(--error-color)'
                    }
                });

                const messageElem = DOM.create('div', 'claude-confirm-message', {
                    html: message,
                    style: {
                        fontSize: '16px',
                        lineHeight: '1.6',
                        marginBottom: '28px'
                    }
                });

                const buttonContainer = DOM.create('div', 'claude-modal-buttons', {
                    style: {
                        display: 'flex',
                        justifyContent: 'center',
                        gap: '14px'
                    }
                });

                const cancelButton = DOM.create('button', 'claude-button secondary', {
                    text: cancelText,
                    style: {
                        padding: '12px 24px',
                        border: 'none',
                        borderRadius: '12px',
                        cursor: 'pointer',
                        fontSize: '15px',
                        fontWeight: '500',
                        backgroundColor: 'var(--button-bg)',
                        color: 'var(--text-color)',
                        transition: `all 250ms ${CONFIG.physics.snap}`,
                        boxShadow: 'var(--button-shadow)'
                    }
                });

                DOM.on(cancelButton, 'mouseenter', () => {
                    cancelButton.style.backgroundColor = 'var(--button-hover)';
                    cancelButton.style.transform = 'translateY(-2px)';
                });

                DOM.on(cancelButton, 'mouseleave', () => {
                    cancelButton.style.backgroundColor = 'var(--button-bg)';
                    cancelButton.style.transform = '';
                });

                DOM.on(cancelButton, 'click', () => {
                    DOM.transition(overlay, { opacity: 0 }, 250, CONFIG.physics.gentleIn);
                    DOM.transition(content,
                        { transform: 'scale(0.85) translateY(20px)' },
                        250, CONFIG.physics.exit,
                        () => {
                            DOM.remove(overlay);
                            resolve(false);
                        }
                    );
                });

                const confirmButton = DOM.create('button', 'claude-button primary', {
                    text: confirmText,
                    style: {
                        padding: '12px 24px',
                        border: 'none',
                        borderRadius: '12px',
                        cursor: 'pointer',
                        fontSize: '15px',
                        fontWeight: '500',
                        backgroundColor: 'var(--error-color)',
                        color: 'white',
                        transition: `all 250ms ${CONFIG.physics.snap}`,
                        boxShadow: 'var(--button-shadow)'
                    }
                });

                DOM.on(confirmButton, 'mouseenter', () => {
                    confirmButton.style.filter = 'brightness(1.1)';
                    confirmButton.style.transform = 'translateY(-2px)';
                });

                DOM.on(confirmButton, 'mouseleave', () => {
                    confirmButton.style.filter = '';
                    confirmButton.style.transform = '';
                });

                DOM.on(confirmButton, 'click', () => {
                    DOM.transition(overlay, { opacity: 0 }, 250, CONFIG.physics.gentleIn);
                    DOM.transition(content,
                        { transform: 'scale(0.85) translateY(20px)' },
                        250, CONFIG.physics.exit,
                        () => {
                            DOM.remove(overlay);
                            resolve(true);
                        }
                    );
                });

                DOM.appendAll(buttonContainer, [cancelButton, confirmButton]);
                DOM.appendAll(content, [warningIcon, titleElem, messageElem, buttonContainer]);
                DOM.append(overlay, content);
                document.body.appendChild(overlay);

                void overlay.offsetWidth; // 强制回流

                overlay.style.opacity = '1';
                content.style.transform = 'scale(1) translateY(0)';
            });
        },

        modal(title, body, includeClose = true) {
            const overlay = DOM.create('div', 'claude-modal', {
                style: {
                    position: 'fixed',
                    top: 0, left: 0, right: 0, bottom: 0,
                    backgroundColor: 'var(--modal-overlay)',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 10001,
                    opacity: 0,
                    backdropFilter: 'blur(5px)',
                    transition: `opacity 300ms ${CONFIG.physics.gentleIn}`
                }
            });

            const content = DOM.create('div', 'claude-modal-content', {
                style: {
                    backgroundColor: 'var(--bg-color)',
                    padding: '28px',
                    borderRadius: '16px',
                    boxShadow: 'var(--float-shadow)',
                    width: '500px',
                    maxWidth: '90%',
                    maxHeight: '85vh',
                    overflowY: 'auto',
                    position: 'relative',
                    transform: 'scale(0.9) translateY(20px)',
                    transition: `transform 350ms ${CONFIG.physics.spring}`
                }
            });

            const titleElem = DOM.create('h2', '', {
                text: title,
                style: {
                    margin: '0 0 24px 0',
                    color: 'var(--text-color)',
                    fontSize: '22px',
                    fontWeight: '600',
                    paddingBottom: '12px',
                    borderBottom: '1px solid var(--border)',
                    paddingRight: '30px'
                }
            });

            DOM.append(content, titleElem);

            if (includeClose) {
                // 优化的关闭按钮 - 使用CSS处理动画，解决闪烁问题
				const closeButton = DOM.create('button', 'claude-close-button', {
					html: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
						<line x1="18" y1="6" x2="6" y2="18"></line>
						<line x1="6" y1="6" x2="18" y2="18"></line>
					</svg>`,
					'aria-label': '关闭'
				});

                const closeModal = () => {
                    DOM.transition(overlay, { opacity: 0 }, 250, CONFIG.physics.gentleIn);
                    DOM.transition(content,
                        { transform: 'scale(0.9) translateY(10px)' },
                        250, CONFIG.physics.exit,
                        () => DOM.remove(overlay)
                    );
                };

                DOM.on(closeButton, 'click', closeModal);
                DOM.append(content, closeButton);
            }

            const buttonContainer = DOM.create('div', 'claude-modal-buttons', {
                style: {
                    display: 'flex',
                    justifyContent: 'flex-end',
                    gap: '14px',
                    marginTop: '24px'
                }
            });

            DOM.append(content, body);
            DOM.append(content, buttonContainer);
            DOM.append(overlay, content);
            document.body.appendChild(overlay);

            void overlay.offsetWidth; // 强制回流

            overlay.style.opacity = '1';
            content.style.transform = 'scale(1) translateY(0)';

            return {
                overlay,
                content,
                buttonContainer,
                close() {
                    DOM.transition(overlay, { opacity: 0 }, 250, CONFIG.physics.gentleIn);
                    DOM.transition(content,
                        { transform: 'scale(0.9) translateY(10px)' },
                        250, CONFIG.physics.exit,
                        () => DOM.remove(overlay)
                    );
                }
            };
        },

        tabModal(title, tabs) {
            const container = DOM.create('div');
            const modal = this.modal(title, container);

            const tabsContainer = DOM.create('div', 'claude-tabs-container', {
                style: {
                    display: 'flex',
                    borderBottom: '1px solid var(--border)',
                    marginBottom: '20px'
                }
            });

            const contentContainer = DOM.create('div', 'claude-tabs-content', {
                style: {
                    minHeight: '200px'
                }
            });

            DOM.append(container, tabsContainer);
            DOM.append(container, contentContainer);

            const tabButtons = [];

            tabs.forEach((tab, index) => {
                const tabButton = DOM.create('button', `claude-tab-button ${index === 0 ? 'active' : ''}`, {
                    text: tab.title,
                    style: {
                        padding: '12px 18px',
                        border: 'none',
                        borderBottom: `3px solid ${index === 0 ? 'var(--primary)' : 'transparent'}`,
                        background: 'transparent',
                        cursor: 'pointer',
                        fontSize: '15px',
                        fontWeight: index === 0 ? '600' : '400',
                        color: index === 0 ? 'var(--primary)' : 'var(--text-color)',
                        transition: `all 0.3s ${CONFIG.physics.spring}`
                    }
                });

                tabButtons.push(tabButton);
                DOM.append(tabsContainer, tabButton);

                tab.content.style.display = index === 0 ? 'block' : 'none';
                DOM.append(contentContainer, tab.content);

                DOM.on(tabButton, 'click', () => {
                    tabButtons.forEach((btn, idx) => {
                        btn.style.fontWeight = idx === index ? '600' : '400';
                        btn.style.color = idx === index ? 'var(--primary)' : 'var(--text-color)';
                        btn.style.borderBottom = `3px solid ${idx === index ? 'var(--primary)' : 'transparent'}`;
                    });

                    tabs.forEach((t, idx) => {
                        t.content.style.display = idx === index ? 'block' : 'none';
                    });
                });
            });

            return modal;
        }
    };

    // =============== 核心应用 ===============
    class ClaudeTokenManager {
        constructor() {
            // 初始状态
            this.tokens = [];
            this.dropVisible = false;
            this.isDragging = false;
            this.isDarkMode = document.documentElement.getAttribute('data-mode') === 'dark';

            // 交互状态对象
            this.state = {
                buttonHovered: false,
                dropdownHovered: false,
                isProcessingClick: false,
                isClosing: false,
                touchStarted: false,

                // 判断是否应该保持窗口打开
                shouldKeepOpen() {
                    return this.buttonHovered || this.dropdownHovered;
                }
            };

            // 定时器
            this.hoverTimeout = null;
            this.closeTimeout = null;

            // 位置跟踪
            this.buttonLeft = GM_getValue('buttonLeft', 20);
            this.buttonBottom = GM_getValue('buttonBottom', 20);
            this.originLeft = this.buttonLeft + 22;
            this.originBottom = this.buttonBottom + 22;

            // 初始化
            this.injectStyles();
            this.loadTokens();
            this.createUI();
            this.setupEventListeners();
            this.observeThemeChanges();
        }

        /**
         * 注入样式
         */
        injectStyles() {
            const theme = getTheme(this.isDarkMode);

            const styleText = `
                :root {
                    --bg-color: ${theme.bg};
                    --text-color: ${theme.text};
                    --border: ${theme.border};
                    --button-bg: ${theme.buttonBg};
                    --button-hover: ${theme.buttonHover};
                    --primary: ${theme.primary};
                    --primary-dark: ${theme.primaryDark};
                    --primary-light: ${theme.primaryLight};
                    --modal-overlay: ${theme.modalOverlay};
                    --success-color: ${theme.success};
                    --error-color: ${theme.error};
                    --warning-color: ${theme.warning};
                    --info-color: ${theme.info};
                    --pro-bg: ${theme.proBg};
                    --pro-text: ${theme.proText};
                    --pro-border: ${theme.proBorder};
                    --pro-accent: ${theme.proAccent};
                    --pro-shadow: ${theme.proShadow};
                    --shadow: ${theme.shadow};
                    --deep-shadow: ${theme.deepShadow};
                    --button-shadow: ${theme.buttonShadow};
                    --float-shadow: ${theme.floatShadow};
                }

                /* 悬浮按钮 */
                #claude-toggle-button {
                    width: 44px;
                    height: 44px;
                    border-radius: 50%;
                    background-color: var(--primary);
                    color: white;
                    cursor: move;
                    position: fixed;
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: var(--shadow);
                    transition: all 0.35s ${theme.physics.snap};
                    outline: none;
                    padding: 0;
                    user-select: none;
                    touch-action: none;
                    border: none;
                    font-size: 18px;
                    will-change: transform, box-shadow;
                }

                #claude-toggle-button:hover {
                    transform: scale(1.15);
                    box-shadow: var(--deep-shadow);
                }

                #claude-toggle-button:active {
                    transform: scale(0.95);
                    transition: all 0.15s ${theme.physics.enter};
                }

                /* 按钮加载状态 */
                #claude-toggle-button.loading::after {
                    content: '';
                    position: absolute;
                    top: -4px;
                    right: -4px;
                    bottom: -4px;
                    left: -4px;
                    border: 2px solid rgba(255, 255, 255, 0.6);
                    border-radius: 50%;
                    animation: pulse-ring 2.5s ${theme.physics.spring} infinite;
                }

                @keyframes pulse-ring {
                    0% { transform: scale(0.95); opacity: 0.9; }
                    70% { transform: scale(1.15); opacity: 0.2; }
                    100% { transform: scale(0.95); opacity: 0.9; }
                }

                /* 下拉容器 */
                .claude-dropdown-container {
                    position: fixed;
                    background-color: var(--bg-color);
                    padding: 24px;
                    border-radius: 18px;
                    box-shadow: var(--float-shadow);
                    display: none;
                    flex-direction: column;
                    gap: 0;
                    width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                    z-index: 9999;
                    border: 1px solid var(--border);
                    opacity: 0;
                    transform-origin: top left;
                    transform: scale(0.4);
                    transition: opacity ${theme.timings.dropReveal}ms ${theme.physics.spring},
                                transform ${theme.timings.dropReveal}ms ${theme.physics.spring};
                    will-change: transform, opacity;
                    scrollbar-width: thin;
                    scrollbar-color: rgba(127, 127, 127, 0.2) transparent;
                }

                /* 标题容器 */
                .claude-title-container {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-bottom: 16px;
                    margin-bottom: 12px;
                    border-bottom: 1px solid var(--border);
                }

                .claude-title-container h2 {
                    margin: 0;
                    color: var(--text-color);
                    font-size: 20px;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .claude-title-container h2::before {
                    content: "";
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    background-color: var(--primary);
                    border-radius: 50%;
                }

                .claude-ip-display {
                    font-size: 14px;
                    color: var(--text-color);
                    padding: 6px 14px;
                    background-color: var(--button-bg);
                    border-radius: 20px;
                    transition: all 0.3s ${theme.physics.snap};
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    box-shadow: var(--button-shadow);
                }

                .claude-ip-display:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px var(--deep-shadow);
                }

                .claude-ip-display::before {
                    content: "";
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    background-color: var(--info-color);
                    border-radius: 50%;
                    box-shadow: 0 0 4px var(--info-color);
                }

                /* Token 网格容器 */
                .claude-token-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
                    gap: 14px;
                    max-height: calc(2 * (110px + 14px) + 28px);
                    overflow-y: auto;
                    padding: 16px;
                    border: 1px solid var(--border);
                    border-radius: 14px;
                    background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)'};
                    scrollbar-width: thin;
                    scrollbar-color: rgba(127, 127, 127, 0.2) transparent;
                }

                /* 少量Token时的布局优化 */
                .claude-token-grid.few-tokens {
                    max-height: none;
                    overflow-y: visible;
                }

                /* Token 卡片 */
                .claude-token-item {
                    padding: 18px;
                    border-radius: 14px;
                    background-color: var(--bg-color);
                    border: 1px solid var(--border);
                    cursor: pointer;
                    transition: all 0.35s ${theme.physics.spring};
                    position: relative;
                    height: 110px;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    box-shadow: var(--button-shadow);
                    will-change: transform, box-shadow, border-color;
                    transform-origin: center;
                }

                .claude-token-item:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 12px 24px ${this.isDarkMode ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
                    border-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'var(--primary)'};
                }

                .claude-token-item:active {
                    transform: translateY(-2px) scale(0.98);
                    transition: all 0.2s ${theme.physics.exit};
                }

                .claude-token-item.current-token {
                    border: 2px solid var(--primary);
                    background-color: var(--primary-light);
                }

                /* 确保Pro账号选中效果与普通账号一致 */
                .claude-token-item.pro-account.current-token {
                    border: 2px solid var(--primary);
                    background-color: var(--primary-light);
                    box-shadow: var(--button-shadow);
                }

                .claude-token-item.pro-account.current-token:hover {
                    border-color: var(--primary);
                    box-shadow: 0 12px 24px ${this.isDarkMode ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
                }

                /* Pro 账号卡片样式 - 简约白底设计，边框与普通卡片相同 */
                .claude-token-item.pro-account {
                    border: 1px solid var(--border);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.05);
                    background-color: var(--bg-color);
                }

                .claude-token-item.pro-account:hover {
                    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12), 0 3px 8px rgba(0, 0, 0, 0.08);
                    border-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'var(--primary)'};
                }

                /* 确保不会覆盖current-token的样式 */
                .claude-token-item.pro-account:not(.current-token) {
                    border: 1px solid var(--border);
                }

                /* 测试动画优化 */
                .claude-token-item.testing {
                    position: relative;
                    z-index: 1;
                }

                .claude-token-item.testing::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: ${this.isDarkMode ? 'rgba(66, 153, 225, 0.15)' : 'rgba(66, 153, 225, 0.05)'};
                    border-radius: 14px;
                    z-index: -1;
                    animation: test-pulse 1.5s infinite ${theme.physics.gentleIn};
                    box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.7);
                }

                @keyframes test-pulse {
                    0%, 100% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.7); }
                    50% { box-shadow: 0 0 0 10px rgba(66, 153, 225, 0); }
                }

                .claude-token-item.test-complete {
                    animation: test-complete 0.45s ${theme.physics.snap} forwards;
                }

                @keyframes test-complete {
                    0%, 100% { transform: translateY(-4px); }
                    50% { transform: translateY(-8px); }
                }

                .current-token-badge {
                    position: absolute;
                    top: -10px;
                    left: 10px;
                    background-color: var(--primary);
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transform: scale(1);
                    transition: transform 0.3s ${theme.physics.bounce};
                    z-index: 1;
                }

                .claude-token-item:hover .current-token-badge {
                    transform: scale(1.2) rotate(10deg);
                }

                .current-token-badge::after {
                    content: "";
                    display: block;
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background-color: white;
                    box-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
                }

                /* Pro 标识样式 - 简约设计，使用"Pro"而非"PRO" */
                .claude-pro-badge {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    padding: 3px 9px;
                    border-radius: 8px;
                    font-size: 11px;
                    font-weight: 600; /* 恢复加粗效果 */
                    letter-spacing: 0.02em;
                    text-transform: none; /* 禁用全大写 */
                    background-color: var(--pro-bg);
                    color: var(--pro-text);
                    border: 1px solid var(--border);
                    transition: all 0.3s ${theme.physics.spring};
                    margin-left: 8px;
                    box-shadow: var(--pro-shadow);
                }

                .claude-token-item:hover .claude-pro-badge {
                    transform: translateY(-2px);
                    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
                }

                /* Token 内容 */
                .token-info {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                    flex: 1;
                    justify-content: space-between;
                }

                .token-top-row {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .token-name-container {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .token-number {
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    background-color: var(--button-bg);
                    transition: all 0.3s ${theme.physics.snap};
                    box-shadow: 0 1px 3px var(--shadow);
                }

                .claude-token-item:hover .token-number {
                    background-color: var(--button-hover);
                    transform: translateX(-2px);
                    box-shadow: 0 2px 5px var(--shadow);
                }

                .token-name {
                    font-weight: 500;
                    font-size: 15px;
                    transition: all 0.3s ${theme.physics.enter};
                }

                .claude-token-item:hover .token-name {
                    color: var(--primary);
                    transform: translateX(2px);
                }

                /* Pro 账号名称样式 - 与普通账号相同 */
                .pro-account .token-name {
                    color: var(--text-color);
                    font-weight: 500; /* 与普通账号保持一致 */
                    letter-spacing: normal;
                }

                .pro-account:hover .token-name {
                    color: var(--primary);
                }

                .token-actions {
                    display: flex;
                    gap: 8px;
                    opacity: 0.6;
                    transition: all 0.3s ${theme.physics.enter};
                    transform: translateX(5px);
                }

                .claude-token-item:hover .token-actions {
                    opacity: 1;
                    transform: translateX(0);
                }

                .token-action-btn {
                    background: transparent;
                    border: none;
                    cursor: pointer;
                    padding: 8px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: var(--text-color);
                    transition: all 0.25s ${theme.physics.snap};
                    position: relative;
                }

                .token-action-btn:hover {
                    transform: scale(1.15);
                    background-color: var(--button-bg);
                }

                .token-action-btn:active {
                    transform: scale(0.9);
                    transition: all 0.15s ${theme.physics.enter};
                }

                .token-action-btn.delete-btn:hover {
                    color: var(--error-color);
                }

                .token-bottom-row {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .token-status {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-left: auto;
                    cursor: pointer;
                    padding: 4px 10px;
                    border-radius: 20px;
                    transition: all 0.3s ${theme.physics.snap};
                    background-color: transparent;
                }

                .token-status:hover {
                    background-color: var(--button-bg);
                    transform: translateY(-2px);
                    box-shadow: var(--button-shadow);
                }

                .status-indicator {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background-color: #888;
                    transition: all 0.3s ${theme.physics.bounce};
                }

                .token-status:hover .status-indicator {
                    transform: scale(1.3);
                }

                .status-indicator.success {
                    background-color: var(--success-color);
                    box-shadow: 0 0 8px var(--success-color);
                }

                .status-indicator.error {
                    background-color: var(--error-color);
                    box-shadow: 0 0 8px var(--error-color);
                }

                .status-indicator.loading {
                    background-color: var(--info-color);
                    animation: pulse 2s ${theme.physics.gentleIn} infinite;
                    box-shadow: 0 0 8px var(--info-color);
                }

                @keyframes pulse {
                    0% { opacity: 0.4; transform: scale(0.8); }
                    50% { opacity: 1; transform: scale(1.4); }
                    100% { opacity: 0.4; transform: scale(0.8); }
                }

                .token-time {
                    font-size: 12px;
                    color: var(--text-color);
                    opacity: 0.7;
                    transition: all 0.25s ${theme.physics.enter};
                }

                .claude-token-item:hover .token-time {
                    opacity: 0.9;
                    transform: translateX(-2px);
                }

                /* 账号类型显示 - 简约白底设计 */
                .account-type-indicator {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    font-size: 12px;
                    opacity: 0.8;
                    margin-left: 10px;
                    padding: 1px 8px;
                    border-radius: 8px;
                    background-color: var(--button-bg);
                    transition: all 0.3s ${theme.physics.snap};
                }

                .pro-account .account-type-indicator {
                    color: var(--pro-text);
                    background-color: var(--pro-bg);
                    border: 1px solid var(--border);
                    opacity: 0.95;
                    box-shadow: var(--pro-shadow);
                }

                .claude-token-item:hover .account-type-indicator {
                    opacity: 1;
                    transform: translateX(2px);
                }

                /* 按钮容器 */
                .claude-button-container {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 12px;
                    padding-top: 20px;
                }

                .claude-button {
                    padding: 12px 14px;
                    border: none;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: all 0.3s ${theme.physics.snap};
                    font-size: 14px;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                    white-space: nowrap;
                    overflow: hidden;
                    position: relative;
                    will-change: transform, box-shadow, background-color;
                    box-shadow: var(--button-shadow);
                    min-width: 100px;
                }

                .claude-modal-buttons .claude-button {
                    min-width: 120px;
                    padding: 12px 20px;
                }

                .claude-button:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 8px 16px ${this.isDarkMode ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.15)'};
                }

                .claude-button:active {
                    transform: translateY(-1px) scale(0.98);
                    transition: all 0.15s ${theme.physics.enter};
                }

                .claude-button.primary {
                    background-color: var(--primary);
                    color: white;
                }

                .claude-button.primary:hover {
                    background-color: var(--primary-dark);
                }

                .claude-button.primary:disabled {
                    background-color: #d9a799;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }

                .claude-button.secondary {
                    background-color: var(--button-bg);
                    color: var(--text-color);
                }

                .claude-button.secondary:hover {
                    background-color: var(--button-hover);
                }

                .claude-button svg {
                    transition: transform 0.3s ${theme.physics.bounce};
                }

                .claude-button:hover svg {
                    transform: scale(1.15);
                }

                /* 工具提示 - 修复遮挡问题 */
                [data-tooltip] {
                    position: relative;
                }

                [data-tooltip]::after {
                    content: attr(data-tooltip);
                    position: fixed; /* 改为固定定位，避免受滚动容器影响 */
                    bottom: auto; /* 移除相对定位 */
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 8px 14px;
                    border-radius: 8px;
                    font-size: 12px;
                    white-space: nowrap;
                    z-index: 10005; /* 提高z-index确保在最上层 */
                    opacity: 0;
                    visibility: hidden;
                    pointer-events: none;
                    transition: all 0.3s ${theme.physics.spring};
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                }

                [data-tooltip]:hover::after {
                    opacity: 1;
                    visibility: visible;
                    margin-top: -40px; /* 使用固定偏移 */
                }

                /* 标签样式 */
                .claude-tabs-container {
                    display: flex;
                    border-bottom: 1px solid var(--border);
                    margin-bottom: 20px;
                }

                .claude-tab-button {
                    padding: 12px 18px;
                    border: none;
                    background: transparent;
                    cursor: pointer;
                    font-size: 15px;
                    color: var(--text-color);
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s ${theme.physics.spring};
                }

                .claude-tab-button:hover {
                    color: var(--primary);
                }

                .claude-tab-button.active {
                    font-weight: 600;
                    color: var(--primary);
                    border-bottom-color: var(--primary);
                }

                /* 设置模态框输入框样式 */
                .claude-modal input, .claude-modal textarea {
                    width: 100%;
                    padding: 14px;
                    margin-bottom: 18px;
                    border: 1px solid var(--border);
                    border-radius: 10px;
                    font-size: 14px;
                    background-color: var(--bg-color);
                    color: var(--text-color);
                    transition: all 0.3s ${theme.physics.spring};
                    box-shadow: 0 2px 6px var(--shadow);
                }

                .claude-modal input:focus, .claude-modal textarea:focus {
                    outline: none;
                    border-color: var(--primary);
                    box-shadow: 0 0 0 3px rgba(184, 74, 51, 0.2), 0 2px 8px var(--shadow);
                    transform: translateY(-2px);
                }

                /* 预览容器 */
                .claude-preview-container {
                    margin-top: 18px;
                    max-height: 220px;
                    overflow-y: auto;
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 18px;
                    background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.01)'};
                    box-shadow: inset 0 2px 6px var(--shadow);
                    scrollbar-width: thin;
                    scrollbar-color: rgba(127, 127, 127, 0.2) transparent;
                }

                .claude-preview-title {
                    font-size: 16px;
                    margin-bottom: 14px;
                    color: var(--text-color);
                    border-bottom: 1px solid var(--border);
                    padding-bottom: 8px;
                    font-weight: 500;
                }

                .claude-preview-item {
                    margin-bottom: 12px;
                    font-size: 14px;
                    padding: 12px;
                    border-radius: 10px;
                    background-color: var(--button-bg);
                    transition: all 0.3s ${theme.physics.spring};
                    box-shadow: 0 1px 4px var(--shadow);
                }

                .claude-preview-item:hover {
                    transform: translateX(6px);
                    background-color: var(--button-hover);
                    box-shadow: 0 4px 10px var(--shadow);
                }

                /* Pro 账号预览项样式 - 白底黑字高级设计 */
                .claude-preview-item.pro-account {
                    border: 1px solid var(--pro-border);
                    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.04);
                    position: relative;
                    background-color: var(--pro-bg);
                }

                .claude-preview-item.pro-account:hover {
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.06);
                }

                .claude-preview-item.pro-account .preview-pro-badge {
                    display: inline-block;
                    padding: 2px 6px;
                    border-radius: 4px;
                    background-color: var(--pro-bg);
                    color: var(--pro-text);
                    font-size: 10px;
                    font-weight: 600;
                    letter-spacing: 0.02em;
                    margin-left: 6px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
                    border: 1px solid var(--border);
                }

                /* 滚动提示 */
                .scroll-indicator {
                    grid-column: 1 / -1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 12px;
                    margin-top: 6px;
                    color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)'};
                    font-size: 13px;
                    background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)'};
                    border-radius: 10px;
                    gap: 10px;
                    transition: all 0.3s ${theme.physics.snap};
                    box-shadow: 0 1px 3px var(--shadow);
                }

                .scroll-indicator:hover {
                    background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)'};
                    transform: translateY(3px);
                    box-shadow: 0 3px 8px var(--shadow);
                }

                .scroll-arrow {
                    animation: bounce 2s infinite ${theme.physics.bounce};
                }

                @keyframes bounce {
                    0%, 100% { transform: translateY(0); }
                    50% { transform: translateY(6px); }
                }

                /* 信息提示部分 */
                .claude-info-section {
                    margin-top: 16px;
                    padding: 12px;
                    background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)'};
                    border-radius: 12px;
                    font-size: 13px;
                    color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)'};
                    text-align: center;
                    border: 1px solid ${this.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};
                    transition: all 0.3s ${theme.physics.snap};
                    box-shadow: 0 1px 5px var(--shadow);
                }

                .claude-info-section:hover {
                    background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)'};
                    transform: translateY(-2px);
                    box-shadow: 0 4px 10px var(--shadow);
                }

                /* 自定义滚动条 */
                .claude-token-grid::-webkit-scrollbar,
                .claude-dropdown-container::-webkit-scrollbar,
                .claude-modal-content::-webkit-scrollbar,
                .claude-preview-container::-webkit-scrollbar {
                    width: 6px;
                    background-color: transparent;
                }

                .claude-token-grid::-webkit-scrollbar-track,
                .claude-dropdown-container::-webkit-scrollbar-track,
                .claude-modal-content::-webkit-scrollbar-track,
                .claude-preview-container::-webkit-scrollbar-track {
                    background: transparent;
                    margin: 4px 0;
                }

                .claude-token-grid::-webkit-scrollbar-thumb,
                .claude-dropdown-container::-webkit-scrollbar-thumb,
                .claude-modal-content::-webkit-scrollbar-thumb,
                .claude-preview-container::-webkit-scrollbar-thumb {
                    background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)'};
                    border-radius: 10px;
                    transition: background-color 0.3s ${theme.physics.gentleIn};
                    opacity: 0.6;
                }

                .claude-token-grid::-webkit-scrollbar-thumb:hover,
                .claude-dropdown-container::-webkit-scrollbar-thumb:hover,
                .claude-modal-content::-webkit-scrollbar-thumb:hover,
                .claude-preview-container::-webkit-scrollbar-thumb:hover {
                    background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'};
                    opacity: 1;
                }

                /* 波浪测试动画 */
                @keyframes wave-test {
                    0%, 100% { transform: translateY(0); }
                    50% { transform: translateY(-10px); }
                }

                /* 测试卡片波动序列动画 */
                .testing-wave-0 { animation: wave-test 0.6s infinite ${theme.physics.spring}; animation-delay: 0ms; }
                .testing-wave-1 { animation: wave-test 0.6s infinite ${theme.physics.spring}; animation-delay: 150ms; }
                .testing-wave-2 { animation: wave-test 0.6s infinite ${theme.physics.spring}; animation-delay: 300ms; }
                .testing-wave-3 { animation: wave-test 0.6s infinite ${theme.physics.spring}; animation-delay: 450ms; }

                /* 反馈动画 */
                .feedback-animation {
                    animation: feedback-pulse 0.6s ${theme.physics.bounce};
                }

                @keyframes feedback-pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }

                /* 添加/导入模态框样式优化 */
                .claude-add-tab, .claude-import-tab {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 0 10px;
                }

                .claude-add-tab .input-group, .claude-import-tab .input-group {
                    width: 100%;
                    margin-bottom: 15px;
                }

                .claude-add-tab .input-label, .claude-import-tab .input-label {
                    display: block;
                    font-weight: 500;
                    margin-bottom: 8px;
                    color: var(--text-color);
                    font-size: 14px;
                }

                .claude-import-instructions {
                    background-color: var(--button-bg);
                    padding: 15px;
                    border-radius: 12px;
                    margin-bottom: 20px;
                    font-size: 14px;
                    line-height: 1.6;
                    width: 100%;
                    border-left: 3px solid var(--primary);
                }

                .claude-import-preview-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    margin-bottom: 10px;
                }

                .claude-import-preview-count {
                    font-size: 13px;
                    font-weight: 600;
                    color: var(--primary);
                    padding: 4px 12px;
                    background-color: var(--primary-light);
                    border-radius: 20px;
                }

                .preview-empty-message {
                    text-align: center;
                    padding: 20px;
                    color: var(--text-color);
                    opacity: 0.7;
                    font-style: italic;
                }

                .prefix-container {
                    display: flex;
                    align-items: center;
                    background-color: var(--button-bg);
                    padding: 10px 15px;
                    border-radius: 10px;
                    margin-bottom: 15px;
                    width: 100%;
                }

                .prefix-label {
                    font-weight: 500;
                    margin-right: 15px;
                    white-space: nowrap;
                }

                .prefix-input {
                    flex: 1;
                    margin-bottom: 0 !important;
                    box-shadow: none !important;
                }

                /* 导出格式选择样式 */
                .claude-export-container input[type="radio"] {
                    margin: 0;
                    width: 16px;
                    height: 16px;
                    accent-color: var(--primary);
                }

                .claude-export-container label:hover {
                    background-color: var(--button-hover) !important;
                    border-color: var(--primary) !important;
                    transform: translateY(-1px);
                }

                .claude-export-container input[type="radio"]:checked + span {
                    color: var(--primary);
                    font-weight: 600;
                }

                /* 优化关闭按钮样式 */
				.claude-close-button {
					position: absolute;
					top: 20px;
					right: 20px;
					width: 38px;
					height: 38px;
					border-radius: 50%;
					border: none;
					background-color: transparent;
					color: var(--text-color);
					cursor: pointer;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0;
					z-index: 10;
					overflow: hidden;
					transition-property: transform, box-shadow;
					transition-duration: 350ms;
					transition-timing-function: ${theme.physics.bounce};
				}

				/* 添加背景遮罩元素 */
				.claude-close-button::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background-color: ${this.isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.06)'};
					border-radius: 50%;
					opacity: 0;
					transform: scale(0);
					transition: transform 400ms ${theme.physics.bounce}, opacity 400ms ease;
					z-index: -1;
				}

				.claude-close-button:hover::before {
					opacity: 1;
					transform: scale(1);
				}

				.claude-close-button svg {
					width: 18px;
					height: 18px;
					stroke: currentColor;
					stroke-width: 2.2;
					transition: transform 450ms ${theme.physics.bounce};
				}

				.claude-close-button:hover {
					transform: scale(1.15);
					box-shadow: 0 2px 8px ${this.isDarkMode ? 'rgba(0, 0, 0, 0.25)' : 'rgba(0, 0, 0, 0.12)'};
				}

				.claude-close-button:hover svg {
					transform: rotate(90deg);
				}

				.claude-close-button:active {
					transform: scale(0.9);
					transition-duration: 180ms;
					transition-timing-function: ${theme.physics.exit};
				}

				.claude-close-button:focus-visible {
					outline: 2px solid var(--primary);
					outline-offset: 2px;
				}
            `;

            GM_addStyle(styleText);
        }

        /**
         * 更新样式（当主题改变时）
         */
        updateStyles() {
            this.injectStyles();
        }

        /**
         * 加载所有Token
         */
        loadTokens() {
            try {
                const savedTokens = GM_getValue(CONFIG.storageKey);
                this.tokens = savedTokens && savedTokens.length > 0
                    ? savedTokens
                    : [CONFIG.defaultToken];

                // 为没有创建时间的token添加默认值
                this.tokens = this.tokens.map(token => {
                    if (!token.createdAt) {
                        const now = new Date();
                        return {
                            ...token,
                            createdAt: now.toLocaleString("zh-CN", {
                                month: "2-digit",
                                day: "2-digit",
                                hour: "2-digit",
                                minute: "2-digit",
                            }),
                            timestamp: now.getTime(),
                            accountType: token.accountType || undefined
                        };
                    }
                    return token;
                });
            } catch (error) {
                console.error("加载 tokens 失败:", error);
                this.tokens = [CONFIG.defaultToken];
            }
        }

        /**
         * 保存所有Token
         */
        saveTokens() {
            try {
                GM_setValue(CONFIG.storageKey, this.tokens);
            } catch (error) {
                console.error("保存 tokens 失败:", error);
                Notify.status("保存 tokens 失败，请重试。", "error");
            }
        }

        /**
         * 创建用户界面
         */
        createUI() {
            // 创建浮动按钮
            this.toggleButton = DOM.create('button', 'claude-toggle-button', {
                id: 'claude-toggle-button',
                html: `
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" pointer-events="none">
                        <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4" pointer-events="none"></path>
                    </svg>
                `,
                style: {
                    left: `${this.buttonLeft}px`,
                    bottom: `${this.buttonBottom}px`
                }
            });
            document.body.appendChild(this.toggleButton);

            // 创建下拉容器
            this.dropdownContainer = DOM.create('div', 'claude-dropdown-container');
            document.body.appendChild(this.dropdownContainer);

            // 创建标题容器
            const titleContainer = DOM.create('div', 'claude-title-container');

            const title = DOM.create('h2', '', { text: 'Claude Session Key 管理器' });
            this.ipDisplay = DOM.create('div', 'claude-ip-display', { text: 'IP: 加载中...' });

            DOM.append(titleContainer, title);
            DOM.append(titleContainer, this.ipDisplay);
            DOM.append(this.dropdownContainer, titleContainer);

            // 创建 Token 网格
            this.tokenGrid = DOM.create('div', 'claude-token-grid');
            DOM.append(this.dropdownContainer, this.tokenGrid);

            // 更新 Token 网格
            this.updateTokenGrid();

            // 创建按钮容器 - 调整为4列布局
            const buttonContainer = DOM.create('div', 'claude-button-container', {
                style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)', // 改为2列布局
                    gap: '12px',
                    paddingTop: '20px'
                }
            });

            // 测试所有按钮 - 移除tooltip
            const testAllButton = DOM.create('button', 'claude-button primary', {
                html: `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 6v6l4 2"></path>
                    </svg> 测活
                `
            });

            DOM.on(testAllButton, 'click', () => this.testAllTokens());
            DOM.append(buttonContainer, testAllButton);

            // 清理无效按钮 - 移除tooltip
            const cleanInvalidButton = DOM.create('button', 'claude-button secondary', {
                html: `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                        <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg> 清理
                `
            });

            DOM.on(cleanInvalidButton, 'click', () => this.removeInvalidTokens());
            DOM.append(buttonContainer, cleanInvalidButton);

            // 添加/导入合并按钮 - 移除tooltip
            const addImportButton = DOM.create('button', 'claude-button secondary', {
                html: `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="16"></line>
                        <line x1="8" y1="12" x2="16" y2="12"></line>
                    </svg> 添加/导入
                `
            });

            DOM.on(addImportButton, 'click', () => this.showAddImportModal());
            DOM.append(buttonContainer, addImportButton);

            // 导出按钮
            const exportButton = DOM.create('button', 'claude-button secondary', {
                html: `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg> 导出
                `
            });

            DOM.on(exportButton, 'click', () => this.exportAllTokens());
            DOM.append(buttonContainer, exportButton);

            DOM.append(this.dropdownContainer, buttonContainer);

            // 添加信息提示
            const infoSection = DOM.create('div', 'claude-info-section', {
                text: '悬停显示面板 • 拖拽按钮调整位置 • 自动识别Pro账号'
            });
            DOM.append(this.dropdownContainer, infoSection);

            // 获取IP信息
            this.fetchIPCountryCode();
        }

        /**
         * 更新Token网格
         */
        updateTokenGrid() {
            this.tokenGrid.innerHTML = '';

            // 获取当前使用的 token
            const currentTokenName = GM_getValue(CONFIG.currentTokenKey);

            // 加载测试结果
            const testResults = this.loadTestResults();

            // 对于少量token优化布局
            if (this.tokens.length <= 2) {
                DOM.addClass(this.tokenGrid, 'few-tokens');
            } else {
                DOM.removeClass(this.tokenGrid, 'few-tokens');
            }

            const fragment = document.createDocumentFragment();

            this.tokens.forEach((token, index) => {
                // 检查是否为Pro账号
                const isPro = token.accountType === "pro";

                // 创建Token卡片
                const tokenItem = DOM.create('div', `claude-token-item ${isPro ? 'pro-account' : ''}`);

                // 添加当前选中状态标记
                if (token.name === currentTokenName) {
                    tokenItem.classList.add('current-token');

                    // 添加选中标记
                    const currentBadge = DOM.create('div', 'current-token-badge');
                    DOM.append(tokenItem, currentBadge);
                }

                // Token 信息容器
                const tokenInfo = DOM.create('div', 'token-info');

                // 顶部行：名称和操作按钮
                const topRow = DOM.create('div', 'token-top-row');

                // 名称容器
                const nameContainer = DOM.create('div', 'token-name-container');

                const numberBadge = DOM.create('span', 'token-number', {
                    text: `#${(index + 1).toString().padStart(2, '0')}`
                });

                const nameSpan = DOM.create('span', 'token-name', {
                    text: token.name
                });

                DOM.append(nameContainer, numberBadge);
                DOM.append(nameContainer, nameSpan);

                // 添加Pro标识 - 使用简洁的设计
                if (isPro) {
                    const proBadge = DOM.create('span', 'claude-pro-badge', {
                        text: 'Pro'
                    });
                    DOM.append(nameContainer, proBadge);
                }

                // 操作按钮
                const actions = DOM.create('div', 'token-actions');

                // 编辑按钮
                const editButton = DOM.create('button', 'token-action-btn edit-btn', {
                    html: `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
                        </svg>
                    `,
                    'data-index': index,
                    'data-tooltip': '编辑Token'
                });

                DOM.on(editButton, 'click', (e) => {
                    e.stopPropagation();
                    this.showEditTokenModal(index);
                });

                // 删除按钮
                const deleteButton = DOM.create('button', 'token-action-btn delete-btn', {
                    html: `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                    `,
                    'data-index': index,
                    'data-tooltip': '删除Token'
                });

                DOM.on(deleteButton, 'click', (e) => {
                    e.stopPropagation();
                    this.confirmDeleteToken(index);
                });

                DOM.append(actions, editButton);
                DOM.append(actions, deleteButton);

                DOM.append(topRow, nameContainer);
                DOM.append(topRow, actions);

                // 底部行：状态和时间
                const bottomRow = DOM.create('div', 'token-bottom-row');

                // 添加时间戳
                const timeSpan = DOM.create('span', 'token-time', {
                    text: token.createdAt || ''
                });
                DOM.append(bottomRow, timeSpan);

                // 状态指示器
                const status = DOM.create('div', 'token-status');
                const statusIndicator = DOM.create('div', 'status-indicator');

                // 检查缓存的测试结果
                const testResult = testResults[token.key];
                if (testResult) {
                    statusIndicator.classList.add(testResult.status);

                    let statusTitle = testResult.status === 'success' ? '有效' : '无效';
                    if (testResult.accountType && testResult.status === 'success') {
                        statusTitle += (testResult.accountType === 'pro' ? ' (Pro)' : ' (Free)');
                    }

                    status.setAttribute('title', testResult.message || statusTitle);
                } else {
                    status.setAttribute('title', '点击测试此Token');
                }

                DOM.append(status, statusIndicator);
                DOM.on(status, 'click', async (e) => {
                    e.stopPropagation();
                    await this.testSingleToken(token, statusIndicator, tokenItem);
                });

                DOM.append(bottomRow, status);

                // 将行添加到信息容器
                DOM.append(tokenInfo, topRow);
                DOM.append(tokenInfo, bottomRow);

                // 将信息容器添加到 token 项
                DOM.append(tokenItem, tokenInfo);

                // 点击切换 token
                DOM.on(tokenItem, 'click', () => this.switchToToken(token));

                // 将 token 项添加到文档片段
                fragment.appendChild(tokenItem);
            });

            // 批量添加所有token项
            this.tokenGrid.appendChild(fragment);

            // 只有当token数量大于4时才显示滚动提示
            if (this.tokens.length > 4) {
                const scrollIndicator = DOM.create('div', 'scroll-indicator', {
                    html: `
                        <div class="scroll-text">向下滚动查看更多 (${this.tokens.length - 4})</div>
                        <div class="scroll-arrow">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M7 13l5 5 5-5"></path>
                                <path d="M7 6l5 5 5-5"></path>
                            </svg>
                        </div>
                    `
                });
                DOM.append(this.tokenGrid, scrollIndicator);
            }
        }

        /**
         * 显示添加/导入结合的模态框 (优化版)
         */
        showAddImportModal() {
            // 创建单个添加的内容
            const addContent = DOM.create('div', 'claude-add-tab');

            // 名称输入组
            const nameGroup = DOM.create('div', 'input-group');
            const nameLabel = DOM.create('label', 'input-label', { text: 'Token 名称' });
            const nameInput = DOM.create('input', '', {
                placeholder: '为Token取一个易记的名称',
                'aria-label': 'Token 名称'
            });
            DOM.append(nameGroup, nameLabel);
            DOM.append(nameGroup, nameInput);

            // 密钥输入组
            const keyGroup = DOM.create('div', 'input-group');
            const keyLabel = DOM.create('label', 'input-label', { text: 'Token 密钥' });
            const keyInput = DOM.create('input', '', {
                placeholder: 'sk-ant-sid...',
                'aria-label': 'Token 密钥'
            });
            DOM.append(keyGroup, keyLabel);
            DOM.append(keyGroup, keyInput);

            DOM.append(addContent, nameGroup);
            DOM.append(addContent, keyGroup);

            // 添加账号类型提示
            const accountTypeNote = DOM.create('div', '', {
                text: '添加后将自动检测 Pro 账号',
                style: {
                    fontSize: '13px',
                    color: 'var(--text-color)',
                    opacity: '0.7',
                    textAlign: 'center',
                    marginTop: '10px',
                    fontStyle: 'italic'
                }
            });
            DOM.append(addContent, accountTypeNote);

            const addButton = DOM.create('button', 'claude-button primary', {
                text: '添加 Token',
                style: {
                    width: '100%',
                    marginTop: '20px',
                    padding: '14px'
                }
            });

            DOM.on(addButton, 'click', async () => {
                if (this.validateInput(nameInput.value, keyInput.value)) {
                    // 获取当前时间并格式化
                    const now = new Date();
                    const formattedTime = now.toLocaleString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                    });

                    const newToken = {
                        name: nameInput.value,
                        key: keyInput.value,
                        createdAt: formattedTime,
                        timestamp: now.getTime(),
                        accountType: undefined // 初始化为未检测
                    };

                    this.tokens.push(newToken);
                    this.saveTokens();
                    this.updateTokenGrid();
                    modal.close();

                    Notify.status(`已添加 Token: ${nameInput.value}，正在检测账号类型...`, 'success');

                    // 添加后自动检测账号类型
                    const lastIndex = this.tokens.length - 1;
                    const tokenItem = document.querySelectorAll('.claude-token-item')[lastIndex];
                    const statusIndicator = tokenItem.querySelector('.status-indicator');

                    // 测试并更新账号类型
                    await this.testSingleToken(newToken, statusIndicator, tokenItem);
                }
            });

            DOM.append(addContent, addButton);

            // 创建批量导入的内容
            const importContent = DOM.create('div', 'claude-import-tab');

            // 导入说明
            const importInstructions = DOM.create('div', 'claude-import-instructions', {
                html: `
                    <strong>批量导入说明：</strong>
                    <ul style="margin: 8px 0 0 18px; padding: 0;">
                        <li>每行粘贴一个Token密钥 (格式: sk-ant-sid...)</li>
                        <li>系统将自动为每个Token分配名称</li>
                        <li>可自定义名称前缀</li>
                        <li>导入后将自动检测 Pro 账号</li>
                    </ul>
                `
            });
            DOM.append(importContent, importInstructions);

            // 前缀设置
            const prefixContainer = DOM.create('div', 'prefix-container');
            const prefixLabel = DOM.create('label', 'prefix-label', { text: '名称前缀:' });
            const prefixInput = DOM.create('input', 'prefix-input', { value: 'Token' });
            DOM.append(prefixContainer, prefixLabel);
            DOM.append(prefixContainer, prefixInput);
            DOM.append(importContent, prefixContainer);

            // 文本区域
            const textareaGroup = DOM.create('div', 'input-group');
            const textareaLabel = DOM.create('label', 'input-label', { text: '粘贴Token列表' });
            const textarea = DOM.create('textarea', '', {
                rows: 6,
                placeholder: 'sk-ant-sid01-xxxxxx\nsk-ant-sid02-xxxxxx',
                style: {
                    width: '100%',
                    fontFamily: 'monospace'
                }
            });
            DOM.append(textareaGroup, textareaLabel);
            DOM.append(textareaGroup, textarea);
            DOM.append(importContent, textareaGroup);

            // 预览标题
            const previewHeader = DOM.create('div', 'claude-import-preview-header');
            const previewTitle = DOM.create('div', '', {
                text: '导入预览',
                style: {
                    fontWeight: '600',
                    fontSize: '15px'
                }
            });
            const previewCount = DOM.create('div', 'claude-import-preview-count', { text: '0 个待导入' });
            DOM.append(previewHeader, previewTitle);
            DOM.append(previewHeader, previewCount);
            DOM.append(importContent, previewHeader);

            // 预览容器
            const previewContainer = DOM.create('div', 'claude-preview-container', {
                style: {
                    maxHeight: '180px',
                    marginTop: '10px',
                    marginBottom: '20px'
                }
            });
            DOM.append(importContent, previewContainer);

            // 导入按钮
            const importButton = DOM.create('button', 'claude-button primary', {
                text: '导入 Token',
                style: {
                    width: '100%',
                    padding: '14px'
                }
            });

            DOM.on(importButton, 'click', async () => {
                const result = this.performBulkImport(
                    textarea.value,
                    prefixInput.value,
                    1 // 起始编号固定为1
                );

                if (result > 0) {
                    modal.close();
                    Notify.status(`已成功导入 ${result} 个 Token，正在检测账号类型...`, 'success');

                    // 导入后批量测试所有 token 以检测账号类型
                    await this.testAllTokens(false); // 静默执行，不显示通知
                }
                else {
                    Notify.status('没有有效的 Token 可导入', 'error');
                }
            });

            DOM.append(importContent, importButton);

            // 更新预览
            const updatePreview = () => {
                const tokens = this.parseTokens(textarea.value);
                const namedTokens = this.applyNamingRule(
                    tokens,
                    prefixInput.value,
                    1 // 起始编号固定为1
                );

                // 更新计数
                previewCount.textContent = `${namedTokens.length} 个待导入`;
                if (namedTokens.length > 0) {
                    previewCount.style.display = 'block';
                } else {
                    previewCount.style.display = 'none';
                }

                // 更新预览内容
                previewContainer.innerHTML = '';

                if (namedTokens.length === 0) {
                    const emptyMessage = DOM.create('div', 'preview-empty-message', {
                        text: '请输入有效的Token密钥...'
                    });
                    DOM.append(previewContainer, emptyMessage);
                    return;
                }

                // 显示最多5个预览项
                const displayTokens = namedTokens.slice(0, 5);
                displayTokens.forEach((token, idx) => {
                    // 使用更简约的预览项样式
                    const previewItem = DOM.create('div', 'claude-preview-item', {
                        html: `
                            <strong>${token.name}</strong>:
                            <span style="font-family: monospace; opacity: 0.8;">${token.key.substring(0, 10)}...${token.key.substring(token.key.length-4)}</span>
                        `
                    });
                    DOM.append(previewContainer, previewItem);
                });

                // 如果有更多token，显示提示
                if (namedTokens.length > 5) {
                    const moreMessage = DOM.create('div', 'claude-preview-item', {
                        text: `...还有 ${namedTokens.length - 5} 个 Token`,
                        style: {
                            textAlign: 'center',
                            fontStyle: 'italic',
                            opacity: 0.8
                        }
                    });
                    DOM.append(previewContainer, moreMessage);
                }
            };

            DOM.on(textarea, 'input', updatePreview);
            DOM.on(prefixInput, 'input', updatePreview);

            // 初始化预览
            setTimeout(updatePreview, 100);

            // 创建标签式模态框
            const modal = Notify.tabModal('添加/导入 Token', [
                { title: '单个添加', content: addContent },
                { title: '批量导入', content: importContent }
            ]);

            // 自动聚焦第一个输入框
            setTimeout(() => nameInput.focus(), 400);
        }

        /**
         * 切换到指定Token
         * @param {Object} token Token对象
         */
        async switchToToken(token) {
            // 显示加载状态
            this.toggleButtonLoading(true);

            try {
                // 检查是否有缓存的测试结果
                const cachedResult = this.getTestResult(token.key);

                // 如果有缓存的测试结果且为无效，提示用户并询问是否继续
                if (cachedResult && cachedResult.status === 'error') {
                    const confirmResult = await Notify.confirm(
                        '警告',
                        `该 Token "${token.name}" 已被标记为无效，是否仍要切换到该 Token？`,
                        '确认切换',
                        '取消'
                    );

                    if (!confirmResult) {
                        this.toggleButtonLoading(false);
                        return;
                    }
                }

                // 保存当前选择的token名称
                GM_setValue(CONFIG.currentTokenKey, token.name);

                // 应用token
                this.applyToken(token.key);

                // 隐藏下拉菜单
                this.hideDropdown();

                // 显示成功消息，包含账号类型
                const accountTypeInfo = token.accountType === 'pro' ? ' (Pro)' :
                                        token.accountType === 'free' ? ' (Free)' : '';
                Notify.status(`已切换到 Token: ${token.name}${accountTypeInfo}`, 'success');
            } catch (error) {
                console.error('切换 Token 失败:', error);
                Notify.status('切换 Token 失败', 'error');
                this.toggleButtonLoading(false);
            }
        }

        /**
         * 应用Token - 修复后的方法，解决在claude.ai不生效的问题
         * @param {string} token Token密钥
         */
        applyToken(token) {
            const hostname = window.location.hostname;

            // 特殊处理claude.ai和claude.asia域名
            if (hostname.includes('claude.ai') || hostname.includes('claude.asia')) {
                let domain = hostname;

                // 提取顶级域名
                if (hostname.includes('claude.ai')) {
                    domain = '.claude.ai';
                } else if (hostname.includes('claude.asia')) {
                    domain = '.claude.asia';
                }

                // 删除现有cookie后再设置
                GM_cookie.delete({
                    name: "sessionKey",
                    domain: domain,
                    path: "/"
                }, () => {
                    // 设置新cookie
                    GM_cookie.set({
                        name: "sessionKey",
                        value: token,
                        domain: domain,
                        path: "/",
                        secure: true,
                        httpOnly: false,
                        sameSite: "lax"
                    }, () => {
                        console.log(`Cookie set for domain: ${domain}`);

                        const targetUrl = `https://${hostname}/chats`;

                        // 确保cookie设置后再刷新
                        setTimeout(() => {
                            window.location.reload();
                        }, 300);
                    });
                });
            } else {
                // 其他域名使用login_token接口
                const targetUrl = `https://${hostname}/login_token?session_key=${token}`;
                window.location.href = targetUrl;
            }
        }

        /**
         * 切换按钮加载状态
         * @param {boolean} isLoading 是否加载中
         */
        toggleButtonLoading(isLoading) {
            DOM.toggleClass(this.toggleButton, 'loading', isLoading);
        }

        /**
         * 测试单个Token
         * @param {Object} token Token对象
         * @param {HTMLElement} statusIndicator 状态指示器元素
         * @param {HTMLElement} tokenItem Token项元素
         * @returns {Object} 测试结果
         */
        async testSingleToken(token, statusIndicator, tokenItem) {
            // 显示加载状态
            statusIndicator.className = 'status-indicator loading';
            tokenItem.classList.add('testing');

            // 查找状态容器并更新提示
            const statusContainer = statusIndicator.parentElement;
            if (statusContainer) {
                statusContainer.setAttribute('title', '正在测试...');
            }

            // 测试 token
            const result = await this.testToken(token.key);

            // 保存测试结果
            this.saveTestResult(token.key, result);

            // 更新token的账号类型
            if (result.status === 'success' && result.accountType) {
                const oldType = token.accountType;
                token.accountType = result.accountType;

                // 如果账号类型发生变化，需要更新UI
                if (oldType !== result.accountType) {
                    // 更新Pro账号样式
                    if (result.accountType === 'pro') {
                        tokenItem.classList.add('pro-account');

                        // 查找名称容器，添加Pro标识
                        const nameContainer = tokenItem.querySelector('.token-name-container');
                        if (nameContainer && !nameContainer.querySelector('.claude-pro-badge')) {
                            const proBadge = DOM.create('span', 'claude-pro-badge', {
                                text: 'Pro'
                            });
                            DOM.append(nameContainer, proBadge);
                        }
                    } else {
                        tokenItem.classList.remove('pro-account');

                        // 移除Pro标识
                        const proBadge = tokenItem.querySelector('.claude-pro-badge');
                        if (proBadge) {
                            DOM.remove(proBadge);
                        }
                    }

                    // 保存更新后的tokens
                    this.saveTokens();
                }
            }

            // 更新状态指示器
            statusIndicator.className = `status-indicator ${result.status}`;

            // 更新状态提示，添加账号类型信息
            if (statusContainer) {
                let statusTitle = result.message || (result.status === 'success' ? '有效' : '无效');
                if (result.accountType && result.status === 'success') {
                    statusTitle += ` (${result.accountType === 'pro' ? 'Pro' : 'Free'})`;
                }
                statusContainer.setAttribute('title', statusTitle);
            }

            // 测试完成，添加反馈动画
            tokenItem.classList.remove('testing');

            // 使用更平滑的完成动画
            const animation = [
                { transform: 'translateY(0)' },
                { transform: 'translateY(-8px)' },
                { transform: 'translateY(0)' }
            ];

            tokenItem.animate(animation, {
                duration: 600,
                easing: CONFIG.physics.bounce
            });

            return result;
        }

        /**
         * 测试所有Token
         * @param {boolean} showNotifications 是否显示通知提示，默认为true
         */
        async testAllTokens(showNotifications = true) {
            // 获取所有 token 项
            const tokenItems = Array.from(this.tokenGrid.querySelectorAll('.claude-token-item'));

            // 禁用测试按钮
            const testButton = this.dropdownContainer.querySelector('.claude-button.primary');
            if (testButton) {
                testButton.disabled = true;
                testButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 6v6l4 2"></path>
                    </svg> 测试中...
                `;
            }

            // 清除所有缓存的测试结果
            GM_setValue(CONFIG.testResultsKey, {});

            let successCount = 0;
            let failCount = 0;
            let proCount = 0;
            let freeCount = 0;

            // 显示测试开始消息
            if (showNotifications) {
                Notify.status(`开始测试 ${tokenItems.length} 个 Token...`, 'info', 2000);
            }

            // 优化波浪动画实现
            for (let i = 0; i < tokenItems.length; i++) {
                const tokenItem = tokenItems[i];
                const index = i;
                const token = this.tokens[index];
                const statusIndicator = tokenItem.querySelector('.status-indicator');

                // 将波浪动画类添加到tokenItem
                tokenItem.classList.add(`testing-wave-${i % 4}`);

                // 等待一小段时间开始测试下一个，创建波浪效果
                await new Promise(resolve => setTimeout(resolve, CONFIG.physicsTimings.testDelay));

                const result = await this.testSingleToken(token, statusIndicator, tokenItem);

                // 测试完成后移除波浪动画类
                tokenItem.classList.remove(`testing-wave-${i % 4}`);

                if (result.status === 'success') {
                    successCount++;
                    if (result.accountType === 'pro') proCount++;
                    else freeCount++;
                }
                else failCount++;
            }

            // 保存更新后的token数据（包含账号类型）
            this.saveTokens();

            // 恢复测试按钮
            if (testButton) {
                testButton.disabled = false;
                testButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 6v6l4 2"></path>
                    </svg> 测活
                `;
            }

            // 显示测试完成消息
            if (showNotifications) {
                const statusMsg = `测试完成: ${successCount} 个有效 (${proCount} Pro/${freeCount} Free), ${failCount} 个无效`;
                Notify.status(statusMsg,
                    failCount === 0 ? 'success' : failCount > successCount ? 'error' : 'info');

                // 给测试按钮添加完成动画
                if (testButton) {
                    DOM.addClass(testButton, 'feedback-animation');
                    setTimeout(() => DOM.removeClass(testButton, 'feedback-animation'), 600);
                }
            }
        }

      async testToken(key) {
          return new Promise((resolve) => {
              GM_xmlhttpRequest({
                  method: "GET",
                  url: "https://claude.ai/api/organizations",
                  headers: {
                      accept:
                      "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                      "accept-language": "en-US,en;q=0.9",
                      "cache-control": "max-age=0",
                      cookie: `sessionKey=${key}`,
                      "user-agent": "Mozilla/5.0 (X11; Linux x86_64)",
                      "sec-fetch-mode": "navigate",
                      "sec-fetch-site": "none",
                  },
                  onload: (response) => {
                      try {
                          if (response.status !== 200) {
                              resolve({ status: "error", message: "无效" });
                              return;
                          }

                          const responseText = response.responseText;

                          if (responseText.toLowerCase().includes("unauthorized")) {
                              resolve({ status: "error", message: "无效" });
                              return;
                          }

                          if (responseText.trim() === "") {
                              resolve({ status: "error", message: "无响应" });
                              return;
                          }

                          try {
                              const data = JSON.parse(responseText);
                              const ok = Array.isArray(data) ? data.length > 0 : Array.isArray(data.organizations) && data.organizations.length > 0;
                              if (ok) {
                                  resolve({ status: "success", message: "有效" });
                                  return;
                              }
                          } catch (e) {
                              resolve({ status: "error", message: "解析失败" });
                              return;
                          }

                          resolve({ status: "error", message: "无效数据" });
                      } catch (error) {
                          console.error("解析响应时发生错误:", error);
                          resolve({ status: "error", message: "测试失败" });
                      }
                  },
                  onerror: (error) => {
                      console.error("请求发生错误:", error);
                      resolve({ status: "error", message: "网络错误" });
                  },
                  ontimeout: () => {
                      resolve({ status: "error", message: "超时" });
                  },
              });
          });
      }

        /**
         * 加载测试结果缓存
         * @returns {Object} 测试结果缓存
         */
        loadTestResults() {
            try {
                const cached = GM_getValue(CONFIG.testResultsKey, {});
                const now = Date.now();
                // 清理过期的测试结果
                return Object.entries(cached).reduce((acc, [key, value]) => {
                    if (now - value.timestamp < CONFIG.testResultExpiry) {
                        acc[key] = value;
                    }
                    return acc;
                }, {});
            } catch (error) {
                console.error('加载测试结果缓存失败:', error);
                return {};
            }
        }

        /**
         * 保存测试结果
         * @param {string} key Token密钥
         * @param {Object} result 测试结果
         */
        saveTestResult(key, result) {
            try {
                const testResults = this.loadTestResults();
                const now = new Date();
                const formattedTime = now.toLocaleString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                });

                testResults[key] = {
                    status: result.status,
                    message: result.message,
                    timestamp: now.getTime(),
                    testTime: formattedTime,
                    accountType: result.accountType // 保存账号类型信息
                };
                GM_setValue(CONFIG.testResultsKey, testResults);
            } catch (error) {
                console.error('保存测试结果失败:', error);
            }
        }

        /**
         * 获取测试结果
         * @param {string} key Token密钥
         * @returns {Object|null} 测试结果
         */
        getTestResult(key) {
            const testResults = this.loadTestResults();
            return testResults[key];
        }

        /**
         * 删除所有无效Token
         */
        async removeInvalidTokens() {
            const confirmResult = await Notify.confirm(
                '确认清理',
                '是否删除所有无效的 Tokens？此操作不可撤销。'
            );

            if (!confirmResult) return;

            // 禁用清理按钮，显示加载状态
            const cleanButton = this.dropdownContainer.querySelector('.claude-button:nth-child(2)');
                        cleanButton.disabled = true;
            cleanButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                </svg> 清理中...
            `;

            const testResults = this.loadTestResults();
            const validTokens = this.tokens.filter((token) => {
                const result = testResults[token.key];
                return !result || result.status === 'success';
            });

            if (validTokens.length === this.tokens.length) {
                Notify.status('没有发现无效的 Tokens', 'info');
                cleanButton.disabled = false;
                cleanButton.innerHTML = `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 6h18"></path>
        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
        <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
    </svg> 清理
`;
                return;
            }

            const removedCount = this.tokens.length - validTokens.length;
            this.tokens = validTokens;
            this.saveTokens();
            this.updateTokenGrid();

            // 恢复按钮状态
            cleanButton.disabled = false;
            cleanButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                </svg> 清理
            `;

            // 添加反馈动画
            Animate.bounce(cleanButton, 0.5);

            // 显示成功消息
            Notify.status(`已成功清理 ${removedCount} 个无效 Token`, 'success');
        }

        /**
         * 显示编辑Token模态框
         * @param {number} index Token索引
         */
        showEditTokenModal(index) {
            const token = this.tokens[index];
            const content = DOM.create('div', 'claude-edit-token-form');

            // 名称输入组
            const nameGroup = DOM.create('div', 'input-group');
            const nameLabel = DOM.create('label', 'input-label', { text: 'Token 名称' });
            const nameInput = DOM.create('input', '', {
                value: token.name,
                placeholder: '为Token取一个易记的名称'
            });
            DOM.append(nameGroup, nameLabel);
            DOM.append(nameGroup, nameInput);

            // 密钥输入组
            const keyGroup = DOM.create('div', 'input-group');
            const keyLabel = DOM.create('label', 'input-label', { text: 'Token 密钥' });
            const keyInput = DOM.create('input', '', {
                value: token.key,
                placeholder: 'sk-ant-sid...'
            });
            DOM.append(keyGroup, keyLabel);
            DOM.append(keyGroup, keyInput);

            // 账号类型显示 - 优化样式
            const accountTypeBadge = DOM.create('div', '', {
                style: {
                    marginTop: '15px',
                    textAlign: 'center',
                    fontSize: '14px'
                }
            });

            if (token.accountType) {
                const badgeClass = token.accountType === 'pro' ? 'claude-pro-badge' : '';
                const badgeStyle = token.accountType === 'pro'
                    ? 'display: inline-flex; align-items: center; justify-content: center; padding: 3px 9px; border-radius: 8px; font-size: 11px; font-weight: 600; background-color: var(--pro-bg); color: var(--pro-text); border: 1px solid var(--border); box-shadow: var(--pro-shadow);'
                    : '';

                accountTypeBadge.innerHTML = `账号类型: <span class="${badgeClass}" style="${badgeStyle}">${token.accountType === 'pro' ? 'Pro' : 'Free'}</span>`;
            } else {
                accountTypeBadge.innerHTML = '账号类型: <span style="opacity: 0.7;">未检测</span>';
            }

            // 账号类型说明
            const accountTypeNote = DOM.create('div', '', {
                text: '修改密钥后将自动重新检测账号类型',
                style: {
                    fontSize: '13px',
                    color: 'var(--text-color)',
                    opacity: '0.7',
                    textAlign: 'center',
                    marginTop: '8px',
                    fontStyle: 'italic'
                }
            });

            DOM.append(content, nameGroup);
            DOM.append(content, keyGroup);
            DOM.append(content, accountTypeBadge);
            DOM.append(content, accountTypeNote);

            // 自动聚焦第一个输入框
            setTimeout(() => nameInput.focus(), 400);

            const modal = Notify.modal('编辑 Token', content);
            modal.content.classList.add('narrow-modal');

            const saveButton = DOM.create('button', 'claude-button primary', {
                text: '保存'
            });

            const cancelButton = DOM.create('button', 'claude-button secondary', {
                text: '取消'
            });

            DOM.on(saveButton, 'click', async () => {
                if (this.validateInput(nameInput.value, keyInput.value)) {
                    const keyChanged = token.key !== keyInput.value;

                    // 保留原有的创建时间和时间戳
                    const updatedToken = {
                        name: nameInput.value,
                        key: keyInput.value,
                        createdAt: token.createdAt || '',
                        timestamp: token.timestamp || Date.now(),
                        accountType: keyChanged ? undefined : token.accountType // 如果密钥改变，重置账号类型
                    };

                    this.tokens[index] = updatedToken;
                    this.saveTokens();
                    this.updateTokenGrid();
                    modal.close();

                    // 显示成功消息
                    Notify.status(`已更新 Token: ${nameInput.value}`, 'success');

                    // 如果密钥改变，重新检测账号类型
                    if (keyChanged) {
                        // 查找对应的DOM元素
                        const tokenItem = document.querySelectorAll('.claude-token-item')[index];
                        if (tokenItem) {
                            const statusIndicator = tokenItem.querySelector('.status-indicator');
                            if (statusIndicator) {
                                // 延迟一点时间再检测，让UI先更新
                                setTimeout(async () => {
                                    Notify.status(`正在检测 Token: ${nameInput.value} 的账号类型...`, 'info');
                                    await this.testSingleToken(updatedToken, statusIndicator, tokenItem);
                                }, 500);
                            }
                        }
                    }
                }
            });

            DOM.on(cancelButton, 'click', () => modal.close());

            DOM.append(modal.buttonContainer, cancelButton);
            DOM.append(modal.buttonContainer, saveButton);

            // 添加回车键提交表单
            const handleKeyDown = (e) => {
                if (e.key === 'Enter') {
                    saveButton.click();
                }
            };

            DOM.on(nameInput, 'keydown', handleKeyDown);
            DOM.on(keyInput, 'keydown', handleKeyDown);
        }

        /**
         * 显示删除Token确认对话框
         * @param {number} index Token索引
         */
        confirmDeleteToken(index) {
            const token = this.tokens[index];
            Notify.confirm(
                '删除确认',
                `您确定要删除 Token "${token.name}" 吗？<br>此操作无法撤销。`
            ).then(confirmed => {
                if (confirmed) {
                    this.deleteToken(index);
                }
            });
        }

        /**
         * 删除Token
         * @param {number} index Token索引
         */
        deleteToken(index) {
            const tokenName = this.tokens[index].name;
            this.tokens.splice(index, 1);
            this.saveTokens();
            this.updateTokenGrid();

            // 显示成功消息
            Notify.status(`已删除 Token: ${tokenName}`, 'success');
        }

        /**
         * 导出所有Token
         */
        exportAllTokens() {
            if (this.tokens.length === 0) {
                Notify.status('没有可导出的 Token', 'warning');
                return;
            }

            // 创建导出内容
            const exportData = {
                exportTime: new Date().toLocaleString('zh-CN'),
                version: '2.2.0',
                totalCount: this.tokens.length,
                tokens: this.tokens.map(token => ({
                    name: token.name,
                    key: token.key,
                    accountType: token.accountType || 'unknown',
                    createdAt: token.createdAt,
                    timestamp: token.timestamp
                }))
            };

            // 创建导出模态框
            const content = DOM.create('div', 'claude-export-container');

            // 导出格式选择
            const formatSection = DOM.create('div', '', {
                style: {
                    marginBottom: '20px',
                    padding: '15px',
                    backgroundColor: 'var(--button-bg)',
                    borderRadius: '12px',
                    border: '1px solid var(--border)'
                }
            });

            const formatTitle = DOM.create('h3', '', {
                text: '选择导出格式',
                style: {
                    margin: '0 0 15px 0',
                    fontSize: '16px',
                    fontWeight: '600'
                }
            });

            const formatOptions = DOM.create('div', '', {
                style: {
                    display: 'flex',
                    gap: '12px',
                    flexWrap: 'wrap'
                }
            });

            // JSON格式选项
            const jsonOption = DOM.create('label', '', {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    cursor: 'pointer',
                    padding: '8px 12px',
                    borderRadius: '8px',
                    backgroundColor: 'var(--bg-color)',
                    border: '1px solid var(--border)',
                    transition: 'all 0.3s ease'
                }
            });

            const jsonRadio = DOM.create('input', '', {
                type: 'radio',
                name: 'exportFormat',
                value: 'json',
                checked: true
            });

            const jsonLabel = DOM.create('span', '', { text: 'JSON格式 (完整数据)' });
            DOM.append(jsonOption, jsonRadio);
            DOM.append(jsonOption, jsonLabel);

            // 纯文本格式选项
            const textOption = DOM.create('label', '', {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    cursor: 'pointer',
                    padding: '8px 12px',
                    borderRadius: '8px',
                    backgroundColor: 'var(--bg-color)',
                    border: '1px solid var(--border)',
                    transition: 'all 0.3s ease'
                }
            });

            const textRadio = DOM.create('input', '', {
                type: 'radio',
                name: 'exportFormat',
                value: 'text'
            });

            const textLabel = DOM.create('span', '', { text: '纯文本 (仅密钥)' });
            DOM.append(textOption, textRadio);
            DOM.append(textOption, textLabel);

            DOM.append(formatOptions, jsonOption);
            DOM.append(formatOptions, textOption);
            DOM.append(formatSection, formatTitle);
            DOM.append(formatSection, formatOptions);

            // 预览区域
            const previewSection = DOM.create('div', '', {
                style: {
                    marginBottom: '20px'
                }
            });

            const previewTitle = DOM.create('h3', '', {
                text: '导出预览',
                style: {
                    margin: '0 0 10px 0',
                    fontSize: '16px',
                    fontWeight: '600'
                }
            });

            const previewContainer = DOM.create('textarea', '', {
                readonly: true,
                style: {
                    width: '100%',
                    height: '200px',
                    padding: '12px',
                    border: '1px solid var(--border)',
                    borderRadius: '8px',
                    backgroundColor: 'var(--button-bg)',
                    fontFamily: 'monospace',
                    fontSize: '12px',
                    resize: 'vertical',
                    color: 'var(--text-color)'
                }
            });

            DOM.append(previewSection, previewTitle);
            DOM.append(previewSection, previewContainer);

            // 统计信息
            const statsSection = DOM.create('div', '', {
                style: {
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '12px',
                    backgroundColor: 'var(--primary-light)',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    fontSize: '14px'
                }
            });

            const proCount = this.tokens.filter(t => t.accountType === 'pro').length;
            const freeCount = this.tokens.filter(t => t.accountType === 'free').length;
            const unknownCount = this.tokens.filter(t => !t.accountType || t.accountType === 'unknown').length;

            const statsText = DOM.create('span', '', {
                text: `总计: ${this.tokens.length} 个 Token`
            });

            const typeStats = DOM.create('span', '', {
                text: `Pro: ${proCount} | Free: ${freeCount} | 未检测: ${unknownCount}`
            });

            DOM.append(statsSection, statsText);
            DOM.append(statsSection, typeStats);

            DOM.append(content, formatSection);
            DOM.append(content, previewSection);
            DOM.append(content, statsSection);

            // 更新预览内容的函数
            const updatePreview = () => {
                const selectedFormat = document.querySelector('input[name="exportFormat"]:checked').value;
                let previewContent = '';

                if (selectedFormat === 'json') {
                    previewContent = JSON.stringify(exportData, null, 2);
                } else {
                    previewContent = this.tokens.map(token => token.key).join('\n');
                }

                previewContainer.value = previewContent;
            };

            // 绑定格式选择事件
            DOM.on(jsonRadio, 'change', updatePreview);
            DOM.on(textRadio, 'change', updatePreview);

            // 初始化预览
            updatePreview();

            // 创建模态框
            const modal = Notify.modal('导出 Token', content);

            // 添加导出按钮
            const exportBtn = DOM.create('button', 'claude-button primary', {
                text: '下载文件'
            });

            const copyBtn = DOM.create('button', 'claude-button secondary', {
                text: '复制到剪贴板'
            });

            const cancelBtn = DOM.create('button', 'claude-button secondary', {
                text: '取消'
            });

            // 导出按钮事件
            DOM.on(exportBtn, 'click', () => {
                const selectedFormat = document.querySelector('input[name="exportFormat"]:checked').value;
                const content = previewContainer.value;
                const filename = selectedFormat === 'json'
                    ? `claude-tokens-${new Date().toISOString().slice(0, 10)}.json`
                    : `claude-tokens-${new Date().toISOString().slice(0, 10)}.txt`;

                this.downloadFile(content, filename);
                modal.close();
                Notify.status(`已导出 ${this.tokens.length} 个 Token`, 'success');
            });

            // 复制按钮事件
            DOM.on(copyBtn, 'click', async () => {
                try {
                    await navigator.clipboard.writeText(previewContainer.value);
                    Notify.status('已复制到剪贴板', 'success');
                } catch (err) {
                    // 降级方案
                    previewContainer.select();
                    document.execCommand('copy');
                    Notify.status('已复制到剪贴板', 'success');
                }
            });

            DOM.on(cancelBtn, 'click', () => modal.close());

            DOM.append(modal.buttonContainer, cancelBtn);
            DOM.append(modal.buttonContainer, copyBtn);
            DOM.append(modal.buttonContainer, exportBtn);
        }

        /**
         * 下载文件
         * @param {string} content 文件内容
         * @param {string} filename 文件名
         */
        downloadFile(content, filename) {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        /**
         * 执行批量导入
         * @param {string} input 输入文本
         * @param {string} namePrefix 名称前缀
         * @param {string} startNumber 起始编号
         * @returns {number} 导入数量
         */
        performBulkImport(input, namePrefix, startNumber) {
            const tokens = this.parseTokens(input);
            const namedTokens = this.applyNamingRule(
                tokens,
                namePrefix,
                parseInt(startNumber)
            );

            if (namedTokens.length === 0) {
                Notify.status('没有有效的 Token 可导入', 'error');
                return 0;
            }

            // 为每个token添加创建时间和accountType初始值
            const now = new Date();
            const formattedTime = now.toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
            });

            const tokensWithTime = namedTokens.map(token => ({
                ...token,
                createdAt: formattedTime,
                timestamp: now.getTime(),
                accountType: undefined // 初始化账号类型为未检测
            }));

            this.tokens = [...this.tokens, ...tokensWithTime];
            this.saveTokens();
            this.updateTokenGrid();

            return namedTokens.length;
        }

        /**
         * 解析Token文本
         * @param {string} input 输入文本
         * @returns {Array} Token数组
         */
        parseTokens(input) {
            return input
                .split('\n')
                .map((line) => line.trim())
                .filter((line) => this.validateTokenKey(line))
                .map((key) => ({ key }));
        }

        /**
         * 应用命名规则
         * @param {Array} tokens Token数组
         * @param {string} namePrefix 名称前缀
         * @param {number} startNumber 起始编号
         * @returns {Array} 处理后的Token数组
         */
        applyNamingRule(tokens, namePrefix, startNumber) {
            return tokens.map((token, index) => {
                const number = startNumber + index;
                const name = `${namePrefix}${number.toString().padStart(2, '0')}`;
                return { ...token, name };
            });
        }

        /**
         * 验证输入
         * @param {string} name Token名称
         * @param {string} key Token密钥
         * @returns {boolean} 是否有效
         */
        validateInput(name, key) {
            if (!name || !key) {
                Notify.status('Token 名称和密钥都要填写！', 'error');
                return false;
            }
            if (!this.validateTokenKey(key)) {
                Notify.status('无效的 Token 密钥格式！', 'error');
                return false;
            }
            return true;
        }

        /**
         * 验证Token密钥格式
         * @param {string} key Token密钥
         * @returns {boolean} 是否有效
         */
        validateTokenKey(key) {
            return /^sk-ant-sid\d{2}-[A-Za-z0-9_-]*$/.test(key);
        }

        /**
         * 获取IP国家/地区代码
         */
        fetchIPCountryCode() {
            this.ipDisplay.textContent = 'IP: 加载中...';

            GM_xmlhttpRequest({
                method: 'GET',
                url: CONFIG.ipApiUrl,
                timeout: 5000,
                onload: (response) => {
                    if (response.status === 200) {
                        const countryCode = response.responseText.trim();
                        this.ipDisplay.textContent = 'IP: ' + countryCode;

                        // 根据国家/地区代码设置不同的颜色
                        if (countryCode === 'CN') {
                            this.ipDisplay.style.color = 'var(--error-color)';
                        } else if (['US', 'CA', 'GB', 'AU', 'NZ'].includes(countryCode)) {
                            this.ipDisplay.style.color = 'var(--success-color)';
                        }
                    } else {
                        this.ipDisplay.textContent = 'IP: 获取失败';
                    }
                },
                onerror: () => {
                    this.ipDisplay.textContent = 'IP: 获取失败';
                },
                ontimeout: () => {
                    this.ipDisplay.textContent = 'IP: 获取超时';
                }
            });
        }

        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 拖拽相关事件
            DOM.on(this.toggleButton, 'mousedown', this.onMouseDown.bind(this));
            DOM.on(document, 'mousemove', this.onMouseMove.bind(this));
            DOM.on(document, 'mouseup', this.onMouseUp.bind(this));

            // 添加触摸支持
            DOM.on(this.toggleButton, 'touchstart', this.onTouchStart.bind(this), { passive: false });
            DOM.on(document, 'touchmove', this.onTouchMove.bind(this), { passive: false });
            DOM.on(document, 'touchend', this.onTouchEnd.bind(this));

            // 定时器
            this.hoverTimeout = null;
            this.closeTimeout = null;

            // 鼠标进入按钮
            DOM.on(this.toggleButton, 'mouseenter', () => {
                if (this.state.isProcessingClick || this.state.touchStarted) return;

                this.state.buttonHovered = true;
                clearTimeout(this.closeTimeout);
                clearTimeout(this.hoverTimeout);

                // 如果下拉窗口未显示或正在关闭中，则显示窗口
                if (!this.dropVisible || this.state.isClosing) {
                    // 如果窗口正在关闭，立即显示
                    if (this.state.isClosing) {
                        this.state.isClosing = false;
                        this.showDropdown();
                    } else {
                        this.hoverTimeout = setTimeout(() => {
                            this.showDropdown();
                        }, CONFIG.physicsTimings.buttonHover);
                    }
                }
            });

            // 鼠标离开按钮
            DOM.on(this.toggleButton, 'mouseleave', () => {
                if (this.state.isProcessingClick || this.state.touchStarted) return;

                this.state.buttonHovered = false;
                clearTimeout(this.hoverTimeout);

                // 检查是否应该关闭弹窗
                if (!this.state.shouldKeepOpen()) {
                    this.scheduleHideDropdown();
                }
            });

            // 按钮点击事件
            DOM.on(this.toggleButton, 'click', (e) => {
                if (this.isDragging) return;

                this.state.isProcessingClick = true;
                clearTimeout(this.hoverTimeout);
                clearTimeout(this.closeTimeout);

                if (this.dropVisible) {
                    this.hideDropdown();
                } else {
                    this.showDropdown();
                }

                setTimeout(() => {
                    this.state.isProcessingClick = false;
                }, 100);
            });

            // 鼠标进入弹窗
            DOM.on(this.dropdownContainer, 'mouseenter', () => {
                if (this.state.isProcessingClick) return;

                this.state.dropdownHovered = true;
                clearTimeout(this.closeTimeout);

                // 如果弹窗在淡出过程中，恢复显示
                if (this.dropVisible && this.dropdownContainer.style.opacity !== '1') {
                    this.dropdownContainer.style.opacity = '1';
                    this.dropdownContainer.style.transform = 'scale(1)';
                }
            });

            // 鼠标离开弹窗
            DOM.on(this.dropdownContainer, 'mouseleave', () => {
                if (this.state.isProcessingClick) return;

                this.state.dropdownHovered = false;

                // 检查是否应该关闭弹窗
                if (!this.state.shouldKeepOpen()) {
                    this.scheduleHideDropdown();
                }
            });

            // 点击其他区域关闭下拉菜单
            DOM.on(document, 'click', (e) => {
                if (
                    this.dropdownContainer.style.display === 'flex' &&
                    !this.dropdownContainer.contains(e.target) &&
                    e.target !== this.toggleButton
                ) {
                    this.state.dropdownHovered = false;
                    this.state.buttonHovered = false;
                    this.hideDropdown();
                }
            });
        }

        /**
         * 鼠标按下事件处理
         * @param {MouseEvent} e 鼠标事件
         */
        onMouseDown(e) {
            if (e.button !== 0) return; // 只处理左键点击

            this.isDragging = true;
            this.state.isDragging = true;
            this.startX = e.clientX;
            this.startY = e.clientY;

            const rect = this.toggleButton.getBoundingClientRect();
            this.offsetX = this.startX - rect.left;
            this.offsetY = this.startY - rect.top;

            this.toggleButton.style.cursor = 'grabbing';

            // 如果鼠标在按钮上，立即显示下拉窗口
            if (this.state.buttonHovered && !this.dropVisible) {
                this.showDropdown();
            }

            // 阻止默认行为和事件冒泡
            e.preventDefault();
            e.stopPropagation();
        }

        /**
         * 触摸开始事件处理
         * @param {TouchEvent} e 触摸事件
         */
        onTouchStart(e) {
            if (e.touches.length !== 1) return; // 仅处理单指触摸

            this.state.touchStarted = true;

            const touch = e.touches[0];
            this.startX = touch.clientX;
            this.startY = touch.clientY;

            const rect = this.toggleButton.getBoundingClientRect();
            this.offsetX = this.startX - rect.left;
            this.offsetY = this.startY - rect.top;

            // 显示触摸反馈
            this.toggleButton.style.transform = 'scale(1.2)';

            // 捕捉开始触摸时间，用于区分点击和拖动
            this.touchStartTime = Date.now();
            this.hasMoved = false;

            e.preventDefault(); // 阻止默认行为
        }

        /**
         * 鼠标移动事件处理
         * @param {MouseEvent} e 鼠标事件
         */
        onMouseMove(e) {
            // 检查鼠标是否在按钮上，用于处理拖动过程中的悬停
            const buttonRect = this.toggleButton.getBoundingClientRect();
            const isOverButton =
                e.clientX >= buttonRect.left &&
                e.clientX <= buttonRect.right &&
                e.clientY >= buttonRect.top &&
                e.clientY <= buttonRect.bottom;

            // 更新悬停状态
            if (isOverButton && !this.state.buttonHovered) {
                this.state.buttonHovered = true;
                // 如果下拉窗口未显示或正在关闭中，则显示窗口
                if (!this.dropVisible || this.state.isClosing) {
                    clearTimeout(this.hoverTimeout);
                    // 如果窗口正在关闭或正在拖动，立即显示
                    if (this.state.isClosing || this.isDragging) {
                        this.state.isClosing = false;
                        this.showDropdown();
                    } else {
                        this.hoverTimeout = setTimeout(() => {
                            this.showDropdown();
                        }, CONFIG.physicsTimings.buttonHover);
                    }
                }
            } else if (!isOverButton && this.state.buttonHovered) {
                this.state.buttonHovered = false;
                clearTimeout(this.hoverTimeout);
                if (!this.state.shouldKeepOpen()) {
                    this.scheduleHideDropdown();
                }
            }

            if (!this.isDragging) return;

            const x = e.clientX - this.offsetX;
            const y = e.clientY - this.offsetY;

            // 计算底部位置
            const bottom = window.innerHeight - y - this.toggleButton.offsetHeight;

            // 确保按钮在窗口范围内
            const maxX = window.innerWidth - this.toggleButton.offsetWidth;
            const maxBottom = window.innerHeight - this.toggleButton.offsetHeight;

            this.buttonLeft = Math.max(0, Math.min(x, maxX));
            this.buttonBottom = Math.max(0, Math.min(bottom, maxBottom));

            // 更新按钮位置
            this.toggleButton.style.left = `${this.buttonLeft}px`;
            this.toggleButton.style.bottom = `${this.buttonBottom}px`;
            this.toggleButton.style.top = 'auto';

            // 更新原点位置，用于动画
            this.originLeft = this.buttonLeft + this.toggleButton.offsetWidth / 2;
            this.originBottom = this.buttonBottom + this.toggleButton.offsetHeight / 2;

            // 如果下拉窗口可见,更新其位置
            if (this.dropVisible) {
                this.updateDropdownPosition();
            }

            e.preventDefault();
        }

        /**
         * 触摸移动事件处理
         * @param {TouchEvent} e 触摸事件
         */
        onTouchMove(e) {
            if (!this.state.touchStarted || e.touches.length !== 1) return;

            const touch = e.touches[0];

            // 计算移动距离
            const deltaX = touch.clientX - this.startX;
            const deltaY = touch.clientY - this.startY;
            const distanceMoved = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            // 如果移动距离超过阈值，标记为拖动
            if (distanceMoved > 10) {
                this.hasMoved = true;
                this.isDragging = true;
                this.state.isDragging = true;

                const x = touch.clientX - this.offsetX;
                const y = touch.clientY - this.offsetY;

                // 计算底部位置
                const bottom = window.innerHeight - y - this.toggleButton.offsetHeight;

                // 确保按钮在窗口范围内
                const maxX = window.innerWidth - this.toggleButton.offsetWidth;
                const maxBottom = window.innerHeight - this.toggleButton.offsetHeight;

                this.buttonLeft = Math.max(0, Math.min(x, maxX));
                this.buttonBottom = Math.max(0, Math.min(bottom, maxBottom));

                // 更新按钮位置
                this.toggleButton.style.left = `${this.buttonLeft}px`;
                this.toggleButton.style.bottom = `${this.buttonBottom}px`;
                this.toggleButton.style.top = 'auto';

                // 更新原点位置，以便动画
                this.originLeft = this.buttonLeft + this.toggleButton.offsetWidth / 2;
                this.originBottom = this.buttonBottom + this.toggleButton.offsetHeight / 2;

                // 如果下拉窗口可见,更新其位置
                if (this.dropVisible) {
                    this.updateDropdownPosition();
                }

                e.preventDefault(); // 阻止页面滚动
            }
        }

        /**
         * 鼠标释放事件处理
         * @param {MouseEvent} e 鼠标事件
         */
        onMouseUp(e) {
            if (!this.isDragging) return;

            this.isDragging = false;
            this.state.isDragging = false;
            this.toggleButton.style.cursor = 'move';

            // 保存位置
            GM_setValue('buttonLeft', this.buttonLeft);
            GM_setValue('buttonBottom', this.buttonBottom);

            // 更新原点位置，用于动画
            this.originLeft = this.buttonLeft + this.toggleButton.offsetWidth / 2;
            this.originBottom = this.buttonBottom + this.toggleButton.offsetHeight / 2;

            // 检查鼠标是否在按钮上
            const buttonRect = this.toggleButton.getBoundingClientRect();
            const isOverButton =
                e.clientX >= buttonRect.left &&
                e.clientX <= buttonRect.right &&
                e.clientY >= buttonRect.top &&
                e.clientY <= buttonRect.bottom;

            // 更新悬停状态
            this.state.buttonHovered = isOverButton;

            // 添加短暂延迟，避免与点击事件冲突
            setTimeout(() => {
                // 如果鼠标在按钮上且下拉窗口未显示，则显示下拉窗口
                if (isOverButton && !this.dropVisible) {
                    this.showDropdown();
                }
                // 否则检查是否应该关闭弹窗
                else if (!this.state.shouldKeepOpen()) {
                    this.scheduleHideDropdown();
                }
            }, 100);

            e.preventDefault();
        }

        /**
         * 触摸结束事件处理
         * @param {TouchEvent} e 触摸事件
         */
        onTouchEnd(e) {
            if (!this.state.touchStarted) return;

            this.state.touchStarted = false;
            this.isDragging = false;
            this.state.isDragging = false;

            // 恢复按钮样式
            setTimeout(() => {
                this.toggleButton.style.transform = '';
            }, 150);

            // 保存位置
            if (this.hasMoved) {
                GM_setValue('buttonLeft', this.buttonLeft);
                GM_setValue('buttonBottom', this.buttonBottom);
            } else {
                // 如果没有移动，则视为点击
                const touchDuration = Date.now() - this.touchStartTime;
                if (touchDuration < 300) { // 小于300ms视为点击
                    if (this.dropVisible) {
                        this.hideDropdown();
                    } else {
                        this.showDropdown();
                    }
                }
            }

            e.preventDefault();
        }

        /**
         * 调度隐藏下拉菜单
         */
        scheduleHideDropdown() {
            if (this.state.isProcessingClick) return;

            clearTimeout(this.closeTimeout);
            this.closeTimeout = setTimeout(() => {
                if (!this.state.shouldKeepOpen()) {
                    this.hideDropdown();
                }
            }, 200);
        }

        /**
         * 显示下拉菜单
         */
        showDropdown() {
            // 立即更新状态
            this.dropVisible = true;
            this.state.isClosing = false;

            // 计算下拉菜单位置
            const buttonRect = this.toggleButton.getBoundingClientRect();

            // 先显示容器但设为初始状态
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.display = 'flex';
            this.dropdownContainer.style.transform = 'scale(0.4)';

            // 计算和设置变换原点
            if (!this.originLeft || !this.originBottom) {
                this.originLeft = buttonRect.left + buttonRect.width / 2;
                this.originBottom = buttonRect.top + buttonRect.height / 2;
            }

            // 更新下拉窗口位置
            this.updateDropdownPosition();

            // 淡入效果，使用弹性曲线
            requestAnimationFrame(() => {
                this.dropdownContainer.style.opacity = '1';
                this.dropdownContainer.style.transform = 'scale(1)';
                this.toggleButton.style.transform = 'scale(1.15)';
            });
        }

        /**
         * 隐藏下拉菜单
         */
        hideDropdown() {
            // 设置正在关闭状态
            this.state.isClosing = true;

            // 添加动画，缩小回悬浮球
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.transform = 'scale(0.4)';
            this.toggleButton.style.transform = '';

            // 等待动画完成后隐藏
            this.closeTimeout = setTimeout(() => {
                if (!this.state.shouldKeepOpen()) {
                    this.dropdownContainer.style.display = 'none';
                    this.dropVisible = false;
                    this.state.isClosing = false;  // 重置关闭状态
                } else {
                    // 如果此时应该保持打开，则恢复显示
                    this.state.isClosing = false;  // 重置关闭状态
                    this.dropdownContainer.style.opacity = '1';
                    this.dropdownContainer.style.transform = 'scale(1)';
                }
            }, CONFIG.physicsTimings.dropHide);
        }

        /**
         * 更新下拉窗口位置
         */
        updateDropdownPosition() {
            const buttonRect = this.toggleButton.getBoundingClientRect();
            const dropdownWidth = 600; // 下拉窗口宽度

            // 获取按钮中心点
            const buttonCenterX = buttonRect.left + buttonRect.width / 2;

            // 计算下拉窗口位置 - 偏移量让窗口不直接盖住按钮
            let left = buttonRect.right + 20;
            let top = buttonRect.top - 20;

            // 检查是否超出右边界
            if (left + dropdownWidth > window.innerWidth) {
                // 如果右边放不下，则放到左边
                left = buttonRect.left - dropdownWidth - 20;
            }

            // 如果左边也放不下，则放到上方或下方
            if (left < 0) {
                if (buttonRect.top > window.innerHeight / 2) {
                    // 按钮在下半屏，放到上方
                    left = Math.max(10, buttonCenterX - dropdownWidth / 2);
                    top = buttonRect.top - this.dropdownContainer.offsetHeight - 20;
                } else {
                    // 按钮在上半屏，放到下方
                    left = Math.max(10, buttonCenterX - dropdownWidth / 2);
                    top = buttonRect.bottom + 20;
                }
            }

            // 确保不超出屏幕边界
            left = Math.max(10, Math.min(left, window.innerWidth - dropdownWidth - 10));
            top = Math.max(10, Math.min(top, window.innerHeight - this.dropdownContainer.offsetHeight - 10));

            // 计算并设置变换原点，使动画以按钮为中心展开
            const transformOriginX = this.originLeft - left;
            const transformOriginY = this.originBottom - top;
            this.dropdownContainer.style.transformOrigin = `${transformOriginX}px ${transformOriginY}px`;

            // 应用新位置，使用平滑动画
            this.dropdownContainer.style.transition = `left 0.3s ${CONFIG.physics.gentleIn}, top 0.3s ${CONFIG.physics.gentleIn}, transform ${CONFIG.physicsTimings.dropReveal}ms ${CONFIG.physics.spring}, opacity ${CONFIG.physicsTimings.dropReveal}ms ${CONFIG.physics.enter}`;
            this.dropdownContainer.style.left = `${left}px`;
            this.dropdownContainer.style.top = `${top}px`;
        }

        /**
         * 监视主题变化
         */
        observeThemeChanges() {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (
                        mutation.type === 'attributes' &&
                        mutation.attributeName === 'data-mode'
                    ) {
                        this.isDarkMode =
                            document.documentElement.getAttribute('data-mode') === 'dark';
                        this.updateStyles();
                    }
                });
            });

            observer.observe(document.documentElement, {
                attributes: true,
                attributeFilter: ['data-mode'],
            });
        }
    }

    // 初始化应用
    const app = new ClaudeTokenManager();
})();